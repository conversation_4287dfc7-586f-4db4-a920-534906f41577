from typing import Dict, Set, Any
from flask import current_app

from app.domain.order.repositories import OrderRepository
from app.domain.order.value_objects import SubOrderStatus
from app.infrastructure.payment.services import WechatPaymentService


class CompareUnshippedOrdersUseCase:
    """比较本地未发货订单与微信支付平台未发货订单用例"""

    def __init__(self, order_repo: OrderRepository, payment_service: WechatPaymentService):
        self.order_repo = order_repo
        self.payment_service = payment_service

    def execute(self) -> Dict[str, Any]:
        """执行比较未发货订单
        
        Returns:
            Dict[str, Any]: 比较结果，包含：
                - is_consistent: 是否一致
        """
        try:
            # 获取本地未发货订单的微信支付ID集合
            local_payment_ids = self._get_local_unshipped_payment_ids()
            
            # 获取微信支付平台未发货订单的微信支付ID集合
            wechat_payment_ids = self._get_wechat_unshipped_payment_ids()
            
            # 计算差异
            local_only = local_payment_ids - wechat_payment_ids
            wechat_only = wechat_payment_ids - local_payment_ids
            
            # 判断是否一致
            is_consistent = len(local_only) == 0 and len(wechat_only) == 0
            
            result = {
                "is_consistent": is_consistent
            }
            
            current_app.logger.info(f"未发货订单比较完成: {result}")
            return result
            
        except Exception as e:
            current_app.logger.error(f"比较未发货订单失败: {str(e)}")
            raise

    def _get_local_unshipped_payment_ids(self) -> Set[str]:
        """获取本地未发货订单的微信支付ID集合
        
        Returns:
            Set[str]: 微信支付交易号集合
        """
        try:
            # 获取所有状态为PAID（已支付待发货）的子订单
            unshipped_sub_orders = self.order_repo.find_sub_orders_by_status(SubOrderStatus.PAID)
            
            payment_ids = set()
            for sub_order in unshipped_sub_orders:
                # 通过子订单获取母订单
                parent_order = self.order_repo.find_by_id(sub_order.order_id)
                if parent_order and parent_order.payment_info and parent_order.payment_info.transaction_id:
                    payment_ids.add(parent_order.payment_info.transaction_id)
            
            current_app.logger.info(f"本地未发货子订单数量: {len(unshipped_sub_orders)}, 对应母订单微信支付ID数量: {len(payment_ids)}")
            return payment_ids
            
        except Exception as e:
            current_app.logger.error(f"获取本地未发货订单失败: {str(e)}")
            raise

    def _get_wechat_unshipped_payment_ids(self) -> Set[str]:
        """获取微信支付平台未发货订单的微信支付ID集合
        
        Returns:
            Set[str]: 微信支付交易号集合
        """
        try:
            # 调用微信支付平台获取未发货订单列表
            unshipped_orders = self.payment_service.get_unshipped_orders()
            
            payment_ids = set()
            for order in unshipped_orders:
                if order.get('transaction_id'):
                    payment_ids.add(order['transaction_id'])
            
            current_app.logger.info(f"微信支付平台未发货订单数量: {len(unshipped_orders)}, 微信支付ID数量: {len(payment_ids)}")
            return payment_ids
            
        except Exception as e:
            current_app.logger.error(f"获取微信支付平台未发货订单失败: {str(e)}")
            raise
