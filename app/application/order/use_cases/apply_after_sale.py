from app.domain.order.exceptions import OrderNotFoundError
from app.domain.order.repositories import OrderRepository
from app.domain.order.value_objects import AfterSaleType
from app.domain.repositories import TransactionManager


class ApplyAfterSaleUseCase:
    """申请售后用例"""

    def __init__(self, order_repo: OrderRepository,
                 db_ctx: TransactionManager,
                 image_service=None):
        self.order_repo = order_repo
        self.db_ctx = db_ctx
        self.image_service = image_service

    def execute(self, user_id: int, order_no: str, apply_type: str,
                description: str, evidence_images: list[str]) -> dict:
        """执行申请售后
        
        Args:
            user_id: 用户ID
            order_no: 子订单号
            apply_type: 申请类型
            description: 申请说明
            evidence_images: 证明图片URL列表，传入的是相对路径
            
        Returns:
            包含处理结果的字典
            
        Raises:
            OrderNotFoundError: 子订单不存在
            InvalidOrderStatusTransitionError: 当前状态不能申请售后
        """
        # 查询主订单和子订单
        parent_order_no = order_no.split('-')[0]
        order = self.order_repo.find_by_order_no(parent_order_no)
        if not order:
            raise OrderNotFoundError(f"订单 {parent_order_no} 不存在")

        if order.user_id != user_id:
            raise OrderNotFoundError(f"订单 {parent_order_no} 不存在")

        # 查找子订单
        sub_order = next((so for so in order.sub_orders if so.order_no == order_no), None)
        if not sub_order:
            raise OrderNotFoundError(f"子订单 {order_no} 不存在")

        # 获取图片完整URL进行业务处理和保存
        full_image_urls = []
        if self.image_service and evidence_images:
            full_image_urls = [self.image_service.get_image_url(image_path) for image_path in evidence_images]
        else:
            full_image_urls = evidence_images

        # 执行申请售后
        order.apply_after_sale(
            sub_order_id=sub_order.id,
            apply_type=AfterSaleType(apply_type),
            description=description,
            evidence_images=full_image_urls,  # 使用完整URL保存到业务数据中
            operator_id=user_id
        )

        with self.db_ctx:
            # 保存订单
            self.order_repo.save(order)

        # 如果提供了图片服务，移除图片的定时删除
        # 注意：remove_image_expiration方法可以处理相对路径或完整URL，会自动提取图片ID
        if self.image_service and evidence_images:
            for image_url in evidence_images:  # 使用原始的相对路径，因为remove_image_expiration会正确处理
                self.image_service.remove_image_expiration(image_url)

        # 返回结果
        return {}
