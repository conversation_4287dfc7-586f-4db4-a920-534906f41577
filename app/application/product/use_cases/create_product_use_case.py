from typing import List, Optional, Dict, Any
from sqlalchemy.orm import sessionmaker
from flask import current_app

from app.domain.repositories import TransactionManager
from app.extensions import db
from app.infrastructure.image_service import ImageService
from app.models import (
    Product, ProductTag, ProductGuarantee, ProductImage, ProductTrace, ProductWiki,
    Channel, ChannelProductAssociation, Region, RegionProductAssociation,
    Category, ProductCategoryAssociation
)


class CreateProductUseCase:
    """管理员新建商品用例"""

    def __init__(self, db_ctx: TransactionManager, image_service: ImageService):
        self.db_ctx = db_ctx
        self.image_service = image_service

    def execute(self, **kwargs) -> Dict[str, Any]:
        """执行新建商品
        
        Args:
            **kwargs: 商品创建参数
            
        Returns:
            包含创建结果的字典
        """
        with self.db_ctx:
            # 创建商品主体
            product = self._create_product(kwargs)
            db.session.add(product)
            db.session.flush()  # 获取product.id

            # 处理标签
            if kwargs.get('service_tag_list'):
                self._handle_tags(product, kwargs['service_tag_list'])

            # 处理购物保障
            if kwargs.get('guarantee_item_list'):
                self._handle_guarantees(product, kwargs['guarantee_item_list'])

            # 处理图片
            self._handle_images(product, kwargs)

            # 处理溯源信息
            if kwargs.get('trace_image_list'):
                self._handle_trace(product, kwargs['trace_image_list'])

            # 处理百科信息
            if kwargs.get('wiki_image_list'):
                self._handle_wiki(product, kwargs['wiki_image_list'])

            # 处理频道关联
            self._handle_channel_associations(product, kwargs)

            # 处理分类关联
            if kwargs.get('classification_id'):
                self._handle_category_association(product, kwargs['classification_id'])

            db.session.commit()

            return {
                "product_id": product.id,
                "message": "商品创建成功"
            }

    def _create_product(self, data: Dict[str, Any]) -> Product:
        """创建商品主体"""
        # 处理图片路径并设置为永久保存
        title_image = self._process_image_path(data['secondly_image'])
        description_image = self._process_image_path(data['primary_image'])

        product = Product(
            title=data['product_name'],
            title_image=title_image,
            subtitle=data.get('product_subtitle', ''),
            description=data['product_description'],
            description_image=description_image,
            origin=data.get('product_origin', ''),
            points_ratio=data.get('point', 1),
            awards_description=data.get('recommend', ''),
            is_published=False,  # 固定为下架
            shop_id=1  # 默认为1
        )
        return product

    def _handle_tags(self, product: Product, tag_names: List[str]):
        """处理标签：先查询是否存在，存在则使用，不存在则新建"""
        for tag_name in tag_names:
            # 查询是否已存在
            existing_tag = db.session.query(ProductTag).filter_by(name=tag_name).first()
            if existing_tag:
                product.tags.append(existing_tag)
            else:
                # 创建新标签
                new_tag = ProductTag(name=tag_name)
                db.session.add(new_tag)
                product.tags.append(new_tag)

    def _handle_guarantees(self, product: Product, guarantee_items: List[Dict[str, str]]):
        """处理购物保障：每个商品都创建新的独立记录"""
        for item in guarantee_items:
            guarantee = ProductGuarantee(
                title=item['title'],
                description=item['content']
            )
            db.session.add(guarantee)
            product.guarantees.append(guarantee)

    def _handle_images(self, product: Product, data: Dict[str, Any]):
        """处理图片列表"""
        # 处理轮播图片
        swiper_images = data.get('swiper_image_list', [])
        for i, image_path in enumerate(swiper_images):
            processed_path = self._process_image_path(image_path)
            product_image = ProductImage(
                product_id=product.id,
                image_url=processed_path,
                is_detail=False,
                display_order=i
            )
            db.session.add(product_image)

        # 处理详情图片
        detail_images = data.get('detail_image_list', [])
        for i, image_path in enumerate(detail_images):
            processed_path = self._process_image_path(image_path)
            product_image = ProductImage(
                product_id=product.id,
                image_url=processed_path,
                is_detail=True,
                display_order=i
            )
            db.session.add(product_image)

    def _handle_trace(self, product: Product, trace_images: List[str]):
        """处理溯源信息"""
        processed_images = [self._process_image_path(img) for img in trace_images]
        trace = ProductTrace(
            product_id=product.id,
            images=processed_images
        )
        db.session.add(trace)

    def _handle_wiki(self, product: Product, wiki_images: List[str]):
        """处理百科信息"""
        processed_images = [self._process_image_path(img) for img in wiki_images]
        wiki = ProductWiki(
            product_id=product.id,
            images=processed_images
        )
        db.session.add(wiki)

    def _handle_channel_associations(self, product: Product, data: Dict[str, Any]):
        """处理频道关联"""
        channel_mappings = [
            ('recommend_channel_id', 'large'),
            ('region_channel_id', 'large'),
            ('navigation_channel_id', 'small')
        ]

        for channel_field, display_type in channel_mappings:
            channel_id = data.get(channel_field)
            if channel_id:
                # 验证频道是否存在
                channel = db.session.query(Channel).filter_by(id=channel_id).first()
                if channel:
                    association = ChannelProductAssociation(
                        channel_id=channel_id,
                        product_id=product.id,
                        display_type=display_type,
                        display_order=0
                    )
                    db.session.add(association)

    def _handle_category_association(self, product: Product, category_id: int):
        """处理分类关联"""
        # 验证分类是否存在
        category = db.session.query(Category).filter_by(id=category_id).first()
        if category:
            association = ProductCategoryAssociation(
                product_id=product.id,
                category_id=category_id
            )
            db.session.add(association)

    def _process_image_path(self, relative_path: str) -> str:
        """处理图片路径：转换为完整路径并设置为永久保存"""
        if not relative_path:
            return relative_path

        # 获取完整URL
        full_url = self.image_service.get_image_url(relative_path)
        
        # 移除图片的临时状态，设置为永久保存
        self.image_service.remove_image_expiration(relative_path)
        
        return full_url
