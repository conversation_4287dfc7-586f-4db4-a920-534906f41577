from sqlalchemy import select, and_

from app.constants import ErrorCode
from app.domain.product.exceptions import SkuNotFoundError
from app.extensions import db
from app.models import SKU
from app.utils.errors import APIError


def update_sku_info_use_case(sku_id, **kwargs) -> dict:
    """更新SKU信息
    
    Args:
        sku_id: SKU ID
        **kwargs: 可更新的字段，包括 price, group_price, postage, commission, actual_stock
    
    Returns:
        dict: 包含操作结果的字典
    """
    # 查询sku是否存在
    stmt = select(SKU).where(
        and_(
            SKU.id == sku_id,
            SKU.deleted_at.is_(None)
        )
    )
    result = db.session.execute(stmt)
    sku = result.scalar_one_or_none()

    # 如果sku不存在，返回错误
    if not sku:
        raise SkuNotFoundError(f"sku不存在或已删除")

    # 过滤掉None值，只更新传入的字段
    update_fields = {k: v for k, v in kwargs.items() if v is not None}

    # 如果没有需要更新的字段，返回成功
    if not update_fields:
        return {}

    # 更新SKU字段
    for field, value in update_fields.items():
        if hasattr(sku, field):
            setattr(sku, field, value)

    try:
        db.session.commit()
        return {}
    except Exception as e:
        db.session.rollback()
        raise APIError(ErrorCode.DATABASE_CONNECTION_FAILED, "更新SKU信息失败")
