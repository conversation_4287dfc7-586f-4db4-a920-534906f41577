from sqlalchemy import select, and_

from app.constants import ErrorCode
from app.extensions import db
from app.models import SKU, Product


def update_sku_publish_status_use_case(sku_id, is_published):
    # 查询sku是否存在
    stmt = select(SKU).where(
        and_(
            SKU.id == sku_id,
            SKU.deleted_at.is_(None)
        )
    )
    result = db.session.execute(stmt)
    sku = result.scalar_one_or_none()

    # 如果sku不存在，返回错误
    if not sku:
        return {
            'code': ErrorCode.SKU_NOT_FOUND.code,
            'message': ErrorCode.SKU_NOT_FOUND.message
        }

    # 如果sku当前状态已经是目标状态，返回错误码1
    if sku.is_published == is_published:
        return {
            'code': ErrorCode.SUCCEED.code,
            'message': f"sku已经{'上架' if is_published else '下架'}"
        }

    # 初始化变量，标记是否同步下架了商品
    product_unpublished = False

    # 如果是下架操作，检查商品是否仅剩该SKU一个有效SKU
    if not is_published:
        # 查询该商品下除当前SKU外的有效SKU数量
        stmt = select(SKU).where(
            and_(
                SKU.product_id == sku.product_id,
                SKU.id != sku.id,
                SKU.is_published == True,
                SKU.deleted_at.is_(None)
            )
        )
        result = db.session.execute(stmt)
        other_valid_skus = result.scalars().all()

        # 如果没有其他有效SKU，则同时下架商品
        if not other_valid_skus:
            stmt = select(Product).where(
                and_(
                    Product.id == sku.product_id,
                    Product.deleted_at.is_(None)
                )
            )
            result = db.session.execute(stmt)
            product = result.scalar_one_or_none()

            if product and product.is_published:
                product.is_published = False
                product_unpublished = True

    # 更新sku状态
    sku.is_published = is_published
    db.session.commit()

    return {
        'code': ErrorCode.SUCCESS.code,
        'product_unpublished': product_unpublished
    }
