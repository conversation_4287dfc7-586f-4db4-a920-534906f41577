from sqlalchemy import select, and_

from app.constants import ErrorCode
from app.domain.product.exceptions import ProductNotFoundException, NoValidSkuError
from app.extensions import db
from app.models import Product, SKU as SKUModel
from app.utils.errors import APIError


class UpdateProductPublishStatusUseCase:
    """更新商品上架/下架状态用例"""

    def execute(self, product_id: int, is_published: bool) -> dict:
        """执行用例，更新商品上架/下架状态

        Args:
            product_id: 商品ID
            is_published: 是否上架

        Returns:
            包含状态码和消息的字典
        """
        # 查询商品是否存在
        stmt = select(Product).where(
            and_(
                Product.id == product_id,
                Product.deleted_at.is_(None)
            )
        )
        result = db.session.execute(stmt)
        product = result.scalar_one_or_none()

        # 如果商品不存在，返回错误
        if not product:
            raise ProductNotFoundException("商品不存在或已删除")

        # 如果商品当前状态已经是目标状态，返回错误码1
        if product.is_published == is_published:
            raise APIError(ErrorCode.SUCCEED, f"商品已经{'上架' if is_published else '下架'}")

        # 如果是上架操作，检查商品是否有有效的SKU
        if is_published:
            # 查询该商品下是否有有效的SKU（未被软删除且已上架）
            valid_sku_count = db.session.scalar(
                select(db.func.count())
                .where(SKUModel.product_id == product_id)
                .where(SKUModel.deleted_at.is_(None))
                .where(SKUModel.is_published == True)
            )

            # 如果没有有效的SKU，返回错误
            if not valid_sku_count:
                raise NoValidSkuError("商品没有有效的SKU，无法上架")

        # 更新商品状态
        product.is_published = is_published
        db.session.commit()

        return {}
