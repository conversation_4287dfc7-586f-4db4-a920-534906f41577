import gzip
import os
import time
import uuid
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Dict, Optional

from flask import current_app, g
from orjson import orjson
from wechatpayv3 import WeChatPay, WeChatPayType

from app.application.order.use_cases.order_payment_use_case import OrderPaymentUseCase
from app.domain.order.services import PaymentService
from app.domain.order.value_objects import RefundStatus
from app.domain.user.services import GroupLeaderCommissionService
from app.infrastructure.order.repositories import SQLOrderRepository
from app.infrastructure.repositories import SQLTransactionManager
from app.infrastructure.user.repositories import SQLGroupLeaderCommissionRepository

# 商户证书私钥，此文件不要放置在下面设置的CERT_DIR目录里。
with open('apiclient_key.pem') as f:
    PRIVATE_KEY = f.read()

# 微信支付平台公钥
# 注：如果使用平台公钥模式初始化，需配置此参数。
with open('pub_key.pem') as f:
    PUBLIC_KEY = f.read()


class WechatPaymentService(PaymentService):
    """微信支付服务实现类"""

    def __init__(self):
        self.wxpay = WeChatPay(
            wechatpay_type=WeChatPayType.NATIVE,
            mchid=current_app.config['MCH_ID'],
            private_key=PRIVATE_KEY,
            cert_serial_no=current_app.config['CERT_SERIAL_NO'],  # 商户证书序列号
            apiv3_key=current_app.config['APIV3_KEY'],
            appid=current_app.config['WECHAT_APPID'],
            notify_url=current_app.config['NOTIFY_URL'],
            logger=current_app.logger,
            partner_mode=False,
            proxy=None,
            timeout=(10, 30),  # 建立连接最大超时时间是10s，读取响应的最大超时时间是30s,
            public_key=PUBLIC_KEY,
            public_key_id=current_app.config['PUBLIC_KEY_ID']
        )

    def create_payment(self, order_no: str, amount: Decimal, title: str) -> Dict:
        """创建支付订单"""
        try:
            # 生成支付参数
            timestamp = str(int(time.time()))
            noncestr = str(uuid.uuid4()).replace('-', '')

            # 调用微信支付接口
            code, message = self.wxpay.pay(
                description=f"甄农优选-{title}",
                out_trade_no=order_no,
                amount={'total': int(amount * 100)},  # 微信支付金额单位为分
                pay_type=WeChatPayType.MINIPROG,
                payer={'openid': g.auth['openid']}  # 需要从请求上下文获取用户openid
            )

            result = orjson.loads(message)

            # 生成支付参数
            if code in range(200, 300) and result.get('prepay_id'):
                prepay_id = result.get('prepay_id')
                package = 'prepay_id=' + prepay_id

                # 签名
                sign = self.wxpay.sign(data=[current_app.config['WECHAT_APPID'], timestamp, noncestr, package])

                return {
                    'appId': current_app.config['WECHAT_APPID'],
                    'timeStamp': timestamp,
                    'nonceStr': noncestr,
                    'package': package,
                    'signType': 'RSA',
                    'paySign': sign
                }
            else:
                current_app.logger.error(f"微信支付创建失败: {message}")
                return {}
        except Exception as e:
            current_app.logger.error(f"创建微信支付参数失败: {str(e)}")
            return {}

    def verify_payment(self, order_id: str) -> str:
        """验证支付结果

        Returns:
            str: 支付状态，可能的值包括：
                - SUCCESS: 支付成功
                - REFUND: 转入退款
                - NOTPAY: 未支付
                - CLOSED: 已关闭
                - 其他状态或空字符串表示查询失败
        """
        try:
            # 查询支付结果
            code, message = self.wxpay.query(out_trade_no=order_id)

            if code in range(200, 300):
                result = orjson.loads(message)
                return result.get('trade_state', '')

            return ''
        except Exception as e:
            current_app.logger.error(f"验证支付结果失败: {str(e)}")
            return ''

    def close_payment(self, order_no: str) -> bool:
        """关闭支付订单"""
        try:
            # 调用微信支付关闭订单接口
            code, message = self.wxpay.close(out_trade_no=order_no)

            if code in range(200, 300):
                current_app.logger.info(f"微信支付订单关闭成功: {order_no}")
                return True
            else:
                current_app.logger.error(f"微信支付订单关闭失败: {message}")
                return False
        except Exception as e:
            current_app.logger.error(f"关闭微信支付订单失败: {str(e)}")
            return False

    def refund(self, order_no: str, transaction_id: str, total_amount: Decimal, refund_amount: Decimal,
               out_refund_no: str, reason: str = None) -> dict:
        """微信支付退款

        Args:
            order_no: 订单号
            transaction_id: 微信支付交易号
            total_amount: 订单总金额
            refund_amount: 退款金额
            out_refund_no: 退款单号
            reason: 退款原因

        Returns:
            退款结果，成功时返回包含退款ID的字典
        """
        try:
            # 调用微信支付退款接口
            code, message = self.wxpay.refund(
                notify_url=current_app.config['REFUND_NOTIFY_URL'],
                out_refund_no=out_refund_no,
                amount={
                    'refund': int(refund_amount * 100),  # 退款金额，单位为分
                    'total': int(total_amount * 100),  # 原订单金额，单位为分
                    'currency': 'CNY'
                },
                transaction_id=transaction_id,  # 使用微信支付交易号
                reason=reason,
            )

            # 处理响应
            if code in range(200, 300):
                result = orjson.loads(message)
                current_app.logger.info(f"微信支付退款成功: {order_no}, 退款单号: {out_refund_no}")

                # 安排退款状态查询任务（1分钟后执行第一次查询）
                try:
                    from app.tasks.task_utils import TaskManager
                    TaskManager.send_task(
                        'tasks.refund.query_refund_status',
                        args=(order_no, out_refund_no, 0),  # 初始重试次数为0
                        countdown=60  # 1分钟后执行
                    )
                    current_app.logger.info(f"已安排退款状态查询任务: {order_no}, 退款单号: {out_refund_no}")
                except Exception as task_error:
                    current_app.logger.error(f"安排退款状态查询任务失败: {str(task_error)}")

                return {
                    'success': True,
                    'refund_no': result.get('refund_no'),
                    'refund_time': result.get('create_time', time.strftime('%Y-%m-%dT%H:%M:%S%z'))
                }
            else:
                current_app.logger.error(f"微信支付退款失败: {message}")
                return {
                    'success': False,
                    'error_message': message
                }
        except Exception as e:
            current_app.logger.error(f"创建微信支付退款失败: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }

    def query_refund(self, out_refund_no: str) -> Dict:
        """查询退款状态

        Args:
            out_refund_no: 退款单号

        Returns:
            退款查询结果，包含微信支付文档中定义的必要字段
        """
        try:
            # 调用微信支付退款查询接口
            code, message = self.wxpay.query_refund(
                out_refund_no=out_refund_no
            )

            # 处理响应
            if code in range(200, 300):
                result = orjson.loads(message)
                current_app.logger.info(f"微信支付退款查询成功: {out_refund_no}")

                # 返回完整的退款查询结果，包含微信支付文档中的必要字段
                return {
                    'success': True,
                    'refund_id': result.get('refund_id'),  # 微信支付退款单号
                    'out_refund_no': out_refund_no,  # 商户退款单号
                    'transaction_id': result.get('transaction_id'),  # 微信支付订单号
                    'out_trade_no': result.get('out_trade_no'),  # 商户订单号
                    'channel': result.get('channel'),  # 退款渠道
                    'user_received_account': result.get('user_received_account'),  # 退款入账账户
                    'success_time': result.get('success_time'),  # 退款成功时间
                    'create_time': result.get('create_time'),  # 退款创建时间
                    'status': result.get('status'),  # 退款状态
                    'funds_account': result.get('funds_account'),  # 资金账户
                    'amount': result.get('amount')  # 金额信息
                }
            else:
                current_app.logger.error(f"微信支付退款查询失败: {message}")
                return {
                    'success': False,
                    'error_message': message
                }
        except Exception as e:
            current_app.logger.error(f"查询微信支付退款失败: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }

    def process_payment_notification(self, headers, body) -> dict:
        """处理微信支付回调通知

        Args:
            headers: 请求头
            body: 请求体

        Returns:
            处理结果，成功时返回{'code': 'SUCCESS', 'message': 'OK'}
        """
        try:
            # 使用callback方法处理回调数据
            # 传入原始请求头和请求体
            result = self.wxpay.callback(headers=headers, body=body)

            if result and result.get('event_type') == 'TRANSACTION.SUCCESS':
                # 获取支付结果数据
                resp = result.get('resource', {})
                out_trade_no = resp.get('out_trade_no')
                transaction_id = resp.get('transaction_id')
                total_amount = resp.get('amount', {}).get('total', 0)

                current_app.logger.info(f"支付成功回调: 订单{out_trade_no}已支付")

                # 准备支付数据
                payment_data = {
                    'out_trade_no': out_trade_no,
                    'transaction_id': transaction_id,
                    'total_amount': total_amount
                }

                # 创建并执行用例
                use_case = OrderPaymentUseCase(
                    order_repo=SQLOrderRepository(),
                    db_ctx=SQLTransactionManager(),
                    logger=current_app.logger
                )
                use_case.execute_by_order_no(payment_data)

                return {"code": "SUCCESS", "message": "OK"}
            else:
                current_app.logger.warning(f"支付回调状态异常或验证失败")
                return {"code": "SUCCESS", "message": "OK"}  # 按微信要求返回成功，避免重复通知

        except Exception as e:
            current_app.logger.error(f"处理微信支付回调失败: {str(e)}")
            return {"code": "FAIL", "message": str(e)}

    def process_refund_notification(self, headers, body) -> dict:
        """处理微信支付退款回调通知

        Args:
            headers: 请求头
            body: 请求体

        Returns:
            处理结果，成功时返回{'code': 'SUCCESS', 'message': 'OK'}
        """
        try:
            # 使用callback方法处理回调数据
            # 传入原始请求头和请求体
            result = self.wxpay.callback(headers=headers, body=body)

            if result:
                event_type = result.get('event_type')
                # 检查是否为退款相关事件
                if event_type in ['REFUND.SUCCESS', 'REFUND.ABNORMAL', 'REFUND.CLOSED']:
                    # 获取退款结果数据
                    resp = result.get('resource', {})
                    out_trade_no = resp.get('out_trade_no')  # 商户订单号
                    out_refund_no = resp.get('out_refund_no')  # 商户退款单号
                    refund_id = resp.get('refund_id')  # 微信退款单号
                    refund_status = resp.get('refund_status')  # 退款状态

                    # 记录退款状态变更
                    current_app.logger.info(
                        f"退款状态变更回调: 订单{out_trade_no}, 退款单号{out_refund_no}, 状态: {refund_status}")

                    # 处理业务逻辑 - 更新订单退款状态
                    try:
                        # 获取订单
                        order_repo = SQLOrderRepository()
                        order = order_repo.find_by_order_no(out_trade_no)

                        if order:
                            # 查找子订单
                            sub_order = next((so for so in order.sub_orders if so.order_no == out_refund_no), None)

                            if sub_order and sub_order.refund_info:
                                # 映射微信支付退款状态到系统退款状态
                                status_mapping = {
                                    'SUCCESS': RefundStatus.SUCCESS,
                                    'ABNORMAL': RefundStatus.ABNORMAL,
                                    'CLOSED': RefundStatus.CLOSED
                                }

                                # 获取对应的系统退款状态
                                system_refund_status = status_mapping.get(refund_status)

                                if system_refund_status:
                                    # 更新订单退款状态
                                    with SQLTransactionManager():
                                        order.complete_refund_sub_order(
                                            sub_order_id=sub_order.id,
                                            refund_status=system_refund_status,
                                            user_received_account=resp.get('user_received_account')
                                        )
                                        order_repo.save(order)

                                    current_app.logger.info(
                                        f"已更新订单退款状态: 订单{out_trade_no}, 子订单{out_refund_no}, 状态: {system_refund_status.value}")
                    except Exception as update_error:
                        current_app.logger.error(f"更新订单退款状态失败: {str(update_error)}")

                    # 按微信要求返回成功，避免重复通知
                    return {"code": "SUCCESS", "message": "OK"}
                else:
                    current_app.logger.warning(f"非退款相关回调事件: {event_type}")
                    return {"code": "SUCCESS", "message": "OK"}
            else:
                current_app.logger.warning(f"退款回调验证失败")
                return {"code": "SUCCESS", "message": "OK"}  # 按微信要求返回成功，避免重复通知

        except Exception as e:
            current_app.logger.error(f"处理微信支付退款回调失败: {str(e)}")
            return {"code": "FAIL", "message": str(e)}

    def create_transfer(self, out_bill_no: str, amount: Decimal, user_openid: str, description: str,
                        user_name: str = None) -> dict:
        """创建微信支付商家转账接口

        Args:
            out_bill_no: 商户系统内部单号
            amount: 转账金额（单位：元）
            user_openid: 收款用户的openid
            description: 转账说明
            user_name: 收款用户姓名（当金额>=2000元时必填）

        Returns:
            转账创建结果
        """
        try:
            # 调用微信支付商家转账接口
            # 注意：金额单位为分，需要将元转换为分
            amount_in_cents = int(amount * 100)

            # 添加场景报备信息（针对1005场景：劳务报酬）
            # 必须传入两条明细，一条为"岗位类型"，一条为"报酬说明"
            transfer_scene_report_infos = [
                {
                    "info_type": "岗位类型",
                    "info_content": "甄选官"
                },
                {
                    "info_type": "报酬说明",
                    "info_content": "佣金提现"
                }
            ]

            # 调用微信支付SDK的mch_transfer_bills方法
            code, message = self.wxpay.mch_transfer_bills(
                out_bill_no=out_bill_no,
                transfer_scene_id="1005",  # 劳务报酬
                openid=user_openid,
                transfer_amount=amount_in_cents,  # 转账金额，单位为分
                transfer_remark=description,
                user_name=user_name,  # 对于金额大于2000元的转账，此参数必填
                notify_url=current_app.config['TRANSFER_NOTIFY_URL'],
                transfer_scene_report_infos=transfer_scene_report_infos  # 添加场景报备信息
            )

            if code in range(200, 300):
                result = orjson.loads(message)
                current_app.logger.info(f"微信支付转账创建成功: {out_bill_no}")

                return {
                    'success': True,
                    'transfer_bill_no': result.get('transfer_bill_no'),  # 微信转账单号
                    'out_bill_no': out_bill_no,  # 商户转账单号
                    'create_time': result.get('create_time'),  # 创建时间
                    'status': result.get('state'),  # 转账状态
                    'fail_reason': result.get('fail_reason'),  # 转账失败原因
                    'package_info': result.get('package_info'),  # 转账结果信息
                    'mchid': current_app.config['MCH_ID'],  # 商户号
                    'appid': current_app.config['WECHAT_APPID']  # 应用ID
                }
            else:
                current_app.logger.error(f"微信支付转账创建失败: {message}")
                return {
                    'success': False,
                    'error_message': message
                }
        except Exception as e:
            current_app.logger.error(f"创建微信支付转账失败: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }

    def query_transfer(self, out_bill_no: str) -> Dict:
        """查询微信支付商家转账状态

        Args:
            out_bill_no: 商户系统内部单号

        Returns:
            转账查询结果
        """
        try:
            # 调用微信支付转账查询接口
            code, message = self.wxpay.mch_transfer_bills_query(
                out_bill_no=out_bill_no
            )

            if code in range(200, 300):
                result = orjson.loads(message)
                current_app.logger.info(f"微信支付转账查询成功: {out_bill_no}")

                payment_info = {
                    'out_bill_no': out_bill_no,  # 商户转账单号
                    'transfer_bill_no': result.get('transfer_bill_no'),  # 微信转账单号
                    'status': result.get('state'),  # 转账状态
                    'transfer_amount': result.get('transfer_amount'),  # 转账金额
                    'transfer_remark': result.get('transfer_remark'),
                    'fail_reason': result.get('fail_reason'),  # 转账失败原因
                    'create_time': result.get('create_time'),  # 创建时间
                    'update_time': result.get('update_time')  # 最后一次状态变更时间
                }

                # 尝试更新提现支付信息
                try:
                    commission_service = GroupLeaderCommissionService(
                        commission_repo=SQLGroupLeaderCommissionRepository()
                    )
                    with SQLTransactionManager():
                        commission_service.update_withdraw_payment_info(out_bill_no, payment_info)
                    current_app.logger.info(
                        f"已更新提现支付查询信息: 提现ID {out_bill_no}, 状态: {result.get('state')}")
                except Exception as update_error:
                    current_app.logger.error(f"更新提现支付查询信息失败: {str(update_error)}")

                payment_info.update({'success': True})
                return payment_info
            else:
                current_app.logger.error(f"微信支付转账查询失败: {message}")
                return {
                    'success': False,
                    'error_message': message
                }
        except Exception as e:
            current_app.logger.error(f"查询微信支付转账失败: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }

    def cancel_transfer(self, out_bill_no: str) -> dict:
        """撤销商家转账接口

        Args:
            out_bill_no: 商户系统内部单号

        Returns:
            撤销结果
        """
        try:
            # 首先查询转账状态
            transfer_info = self.query_transfer(out_bill_no)

            # # 检查转账是否可以撤销
            # if not transfer_info.get('success'):
            #     current_app.logger.error(f"查询转账状态失败，无法撤销: {out_bill_no}")
            #     return {
            #         'success': False,
            #         'error_message': '查询转账状态失败，无法撤销'
            #     }

            # status = transfer_info.get('status')
            # # 只有PROCESSING状态的转账才能撤销
            # if status != 'PROCESSING':
            #     current_app.logger.error(f"转账状态为 {status}，不可撤销: {out_bill_no}")
            #     return {
            #         'success': False,
            #         'error_message': f'转账状态为 {status}，不可撤销'
            #     }

            # 调用微信支付SDK的撤销接口
            code, message = self.wxpay.mch_transfer_bills_cancel(out_bill_no=out_bill_no)

            if code in range(200, 300):
                result = orjson.loads(message)
                current_app.logger.info(f"微信支付转账撤销成功: {out_bill_no}")

                # 构建撤销信息
                cancel_info = {
                    'out_bill_no': out_bill_no,  # 商户转账单号
                    'transfer_bill_no': result.get('transfer_bill_no'),  # 微信转账单号
                    'status': result.get('state'),  # 更新撤销状态
                    'update_time': result.get('operate_time')  # 更新时间
                }

                # 尝试更新提现支付信息
                try:
                    commission_service = GroupLeaderCommissionService(
                        commission_repo=SQLGroupLeaderCommissionRepository()
                    )
                    with SQLTransactionManager():
                        commission_service.update_withdraw_payment_info(out_bill_no, cancel_info)
                    current_app.logger.info(f"已更新提现支付撤销信息: 提现ID {out_bill_no}, 状态: CANCELED")
                except Exception as update_error:
                    current_app.logger.error(f"更新提现支付撤销信息失败: {str(update_error)}")

                result.update({'success': True})
                return result
            else:
                current_app.logger.error(f"微信支付转账撤销失败: {message}")
                return {
                    'success': False,
                    'error_message': message
                }
        except Exception as e:
            current_app.logger.error(f"撤销微信支付转账失败: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }

    def process_transfer_notification(self, headers, body) -> dict:
        """处理微信支付转账回调通知

        Args:
            headers: 请求头
            body: 请求体

        Returns:
            处理结果，成功时返回{'code': 'SUCCESS', 'message': 'OK'}
        """
        try:
            # 使用callback方法处理回调数据
            result = self.wxpay.callback(headers=headers, body=body)

            if result:
                event_type = result.get('event_type')
                # 检查是否为转账相关事件
                if event_type == 'MCHTRANSFER.BILL.FINISHED':
                    # 获取转账结果数据
                    resp = result.get('resource', {})
                    out_bill_no = resp.get('out_bill_no')  # 商户单号
                    transfer_bill_no = resp.get('transfer_bill_no')  # 微信转账单号
                    status = resp.get('state')  # 转账状态

                    current_app.logger.info(
                        f"转账状态变更回调: 单号{out_bill_no}, 状态: {status}")

                    withdraw_id = out_bill_no

                    # 准备支付信息
                    payment_info = {
                        'out_bill_no': out_bill_no,
                        'transfer_bill_no': transfer_bill_no,
                        'status': status,
                        'transfer_amount': resp.get('transfer_amount'),
                        'openid': resp.get('openid'),
                        'fail_reason': resp.get('fail_reason'),
                        'create_time': resp.get('create_time'),
                        'update_time': result.get('update_time')
                    }

                    # 创建佣金服务
                    commission_service = GroupLeaderCommissionService(
                        commission_repo=SQLGroupLeaderCommissionRepository()
                    )

                    # 更新提现支付信息
                    try:
                        with SQLTransactionManager():
                            commission_service.update_withdraw_payment_info(withdraw_id, payment_info)
                        current_app.logger.info(f"已更新提现支付信息: 提现ID {withdraw_id}, 状态: {status}")
                    except Exception as update_error:
                        current_app.logger.error(f"更新提现支付信息失败: {str(update_error)}")

                    # 按微信要求返回成功响应
                    return {"code": "SUCCESS", "message": "OK"}
                else:
                    current_app.logger.warning(f"非转账相关回调事件: {event_type}, 回调数据: {result}")
                    return {"code": "SUCCESS", "message": "OK"}  # 按微信要求返回成功，避免重复通知
            else:
                current_app.logger.warning("转账回调验证失败")
                return {"code": "SUCCESS", "message": "OK"}  # 按微信要求返回成功

        except Exception as e:
            current_app.logger.error(f"处理微信支付转账回调失败: {str(e)}")
            return {"code": "FAIL", "message": str(e)}

    def apply_trade_bill(self, bill_date: str) -> Dict:
        """申请交易账单

        Args:
            bill_date: 账单日期，格式：YYYY-MM-DD

        Returns:
            Dict: 申请结果，包含下载链接等信息
        """
        try:
            code, message = self.wxpay.trade_bill(bill_date=bill_date)

            if code in range(200, 300):
                result = orjson.loads(message)
                current_app.logger.info(f"交易账单申请成功: {bill_date}")
                return {
                    'success': True,
                    'download_url': result.get('download_url'),
                    'hash_type': result.get('hash_type'),
                    'hash_value': result.get('hash_value')
                }
            else:
                current_app.logger.error(f"交易账单申请失败: {message}")
                return {
                    'success': False,
                    'error_message': message
                }
        except Exception as e:
            current_app.logger.error(f"申请交易账单失败: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }

    def apply_fundflow_bill(self, bill_date: str) -> Dict:
        """申请资金账单

        Args:
            bill_date: 账单日期，格式：YYYY-MM-DD

        Returns:
            Dict: 申请结果，包含下载链接等信息
        """
        try:
            code, message = self.wxpay.fundflow_bill(bill_date=bill_date)

            if code in range(200, 300):
                result = orjson.loads(message)
                current_app.logger.info(f"资金账单申请成功: {bill_date}")
                return {
                    'success': True,
                    'download_url': result.get('download_url'),
                    'hash_type': result.get('hash_type'),
                    'hash_value': result.get('hash_value')
                }
            else:
                current_app.logger.error(f"资金账单申请失败: {message}")
                return {
                    'success': False,
                    'error_message': message
                }
        except Exception as e:
            current_app.logger.error(f"申请资金账单失败: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }

    def download_bill(self, download_url: str, bill_type: str, bill_date: str) -> Dict:
        """下载账单文件

        Args:
            download_url: 下载链接
            bill_type: 账单类型 (tradebill/fundflowbill)
            bill_date: 账单日期，格式：YYYY-MM-DD

        Returns:
            Dict: 下载结果，包含文件路径等信息
        """
        try:
            # 确保账单存储目录存在
            bill_storage_dir = current_app.config['BILL_STORAGE_DIR']
            os.makedirs(bill_storage_dir, exist_ok=True)

            # 生成文件名：账单类型_日期.srv
            filename = f"{bill_type}_{bill_date}.csv"
            file_path = os.path.join(bill_storage_dir, filename)

            # 调用微信支付SDK下载账单
            code, message = self.wxpay.download_bill(url=download_url)

            if code in range(200, 300) and isinstance(message, bytes):
                try:
                    # 解压GZIP数据
                    decompressed_data = gzip.decompress(message)

                    # 保存解压后的文件
                    with open(file_path, 'wb') as f:
                        f.write(decompressed_data)

                    current_app.logger.info(f"账单下载并解压成功: {filename}")
                    return {
                        'success': True,
                        'file_path': file_path,
                        'filename': filename
                    }
                except gzip.BadGzipFile:
                    current_app.logger.error(f"账单文件格式错误，不是有效的GZIP文件: {filename}")
                    return {
                        'success': False,
                        'error_message': '账单文件格式错误，不是有效的GZIP文件'
                    }
            else:
                current_app.logger.error(f"账单下载失败: {message}")
                return {
                    'success': False,
                    'error_message': str(message)
                }
        except Exception as e:
            current_app.logger.error(f"下载账单失败: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }

    @staticmethod
    def get_bill_file_path(bill_type: str, bill_date: str) -> Optional[str]:
        """获取本地账单文件路径

        Args:
            bill_type: 账单类型 (tradebill/fundflowbill)
            bill_date: 账单日期，格式：YYYY-MM-DD

        Returns:
            Optional[str]: 文件路径，如果文件不存在则返回None
        """
        bill_storage_dir = current_app.config['BILL_STORAGE_DIR']
        filename = f"{bill_type}_{bill_date}.csv"
        file_path = os.path.join(bill_storage_dir, filename)

        if os.path.exists(file_path):
            return file_path
        return None

    def get_unshipped_orders(self) -> list:
        """获取微信支付平台未发货订单列表

        Returns:
            list: 未发货订单列表，每个订单包含 transaction_id 等信息
        """
        try:
            from app.services.wechat import wechat

            # 调用微信API获取未发货订单列表 (order_state=1 表示未发货)
            result = wechat.client.get_order_list(order_state=1)

            # 提取订单列表
            orders = result.get('order_list', [])

            # 处理订单数据，确保每个订单都有 transaction_id
            processed_orders = []
            for order in orders:
                if order.get('transaction_id'):
                    processed_orders.append({
                        'transaction_id': order['transaction_id'],
                        'merchant_id': order.get('merchant_id'),
                        'merchant_trade_no': order.get('merchant_trade_no'),
                        'order_state': order.get('order_state'),
                        'paid_amount': order.get('paid_amount'),
                        'openid': order.get('openid'),
                        'pay_time': order.get('pay_time')
                    })

            current_app.logger.info(
                f"获取微信支付平台未发货订单成功，总数量: {len(orders)}, 有效订单数量: {len(processed_orders)}")
            return processed_orders

        except Exception as e:
            current_app.logger.error(f"获取微信支付平台未发货订单失败: {str(e)}")
            raise

    @staticmethod
    def is_bill_date_valid(bill_date: str) -> Dict:
        """验证账单日期是否有效

        Args:
            bill_date: 账单日期，格式：YYYY-MM-DD

        Returns:
            Dict: 验证结果
        """
        try:
            # 解析日期
            target_date = datetime.strptime(bill_date, '%Y-%m-%d').date()
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)

            # 检查是否是未来日期
            if target_date > today:
                return {
                    'valid': False,
                    'error': '不能查询未来日期的账单'
                }

            # 检查是否是当天
            if target_date == today:
                return {
                    'valid': False,
                    'error': '不能查询当天的账单'
                }

            # 检查是否是昨天
            if target_date == yesterday:
                current_hour = datetime.now().hour
                if current_hour < 10:
                    return {
                        'valid': False,
                        'error': '昨天的账单需要在今天10点后才能查询'
                    }

            # 检查是否超过保留期限
            retention_months = current_app.config['BILL_RETENTION_MONTHS']
            earliest_date = today - timedelta(days=retention_months * 30)  # 简化计算

            if target_date < earliest_date:
                return {
                    'valid': False,
                    'error': f'账单保留期限为{retention_months}个月，无法查询该日期的账单'
                }

            return {'valid': True}

        except ValueError:
            return {
                'valid': False,
                'error': '日期格式错误，请使用YYYY-MM-DD格式'
            }
