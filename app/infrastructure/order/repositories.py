from datetime import datetime
from decimal import Decimal
from typing import Optional, Any

from sqlalchemy import select, func, and_
from sqlalchemy.orm import selectinload

from app.domain.order.aggregates import Order, OrderItem, SubOrder
from app.domain.order.exceptions import OrderNotFoundError
from app.domain.order.repositories import OrderRepository
from app.domain.order.value_objects import (OrderStatus, OrderType, ShippingAddress, PaymentInfo, LogisticsInfo,
                                            OrderOperationLog, SubOrderStatus, RefundInfo, CancelReason, RefundReason,
                                            RefundStatus, RefundChannel, AfterSaleInfo, AfterSaleType,
                                            AfterSaleProcessType)
from app.extensions import db
from app.infrastructure.order.models import OrderModel, OrderItemModel, SubOrderModel


class SQLOrderRepository(OrderRepository):
    """订单仓储SQLAlchemy实现"""

    def __init__(self, order_formatter_service=None):
        self.session = db.session
        self.order_model = OrderModel
        self.order_formatter_service = order_formatter_service

    def save(self, order: Order) -> Order:
        """保存订单

        Args:
            order: 订单聚合根

        Returns:
            保存后的订单聚合根
        """
        if order.id is None:
            # 创建新订单
            order_model = self._create_order_model(order)
            self.session.add(order_model)
            self.session.flush()

            # 获取数据库生成的ID
            order.id = order_model.id
        else:
            # 更新已存在的订单
            stmt = select(OrderModel).where(OrderModel.id == order.id).with_for_update()
            result = self.session.execute(stmt)
            order_model = result.scalar_one_or_none()
            if not order_model:
                raise ValueError(f"Order with id {order.id} not found")

            # 检查乐观锁版本
            if order_model.version != order.version:
                raise ValueError("Optimistic lock conflict")

            order.version += 1
            self._update_order_model(order_model, order)

        # 删除相关缓存
        if self.order_formatter_service and order.order_no:
            self.order_formatter_service.delete_order_related_caches(order)

        return order

    def find_by_id(self, order_id: int) -> Optional[Order]:
        """根据ID查找订单

        Args:
            order_id: 订单ID

        Returns:
            订单聚合根，如果不存在则返回None
        """
        stmt = select(OrderModel).where(
            OrderModel.id == order_id,
            OrderModel.deleted_at == None
        )
        result = self.session.execute(stmt)
        order_model = result.scalar_one_or_none()

        if not order_model:
            return None

        # 使用_to_domain_entity方法转换为领域模型
        return self._to_domain_entity(order_model)

    def find_by_ids(self, order_ids: list[int]) -> list[Order]:
        """根据ID列表批量查找订单

        Args:
            order_ids: 订单ID列表

        Returns:
            订单聚合根列表
        """
        if not order_ids:
            return []

        # 使用in_操作符一次性查询多个订单
        stmt = select(OrderModel).where(
            OrderModel.id.in_(order_ids),
            OrderModel.deleted_at == None
        ).options(
            selectinload(OrderModel.items),  # 预加载 items
            selectinload(OrderModel.sub_orders)  # 预加载 sub_orders
        )

        result = self.session.execute(stmt)
        order_models = result.scalars().all()

        # 转换为领域模型列表
        return [self._to_domain_entity(order_model) for order_model in order_models]

    def find_by_user_id(self, user_id: int, status: Optional[OrderStatus] = None, page: int = None,
                        per_page: int = None) -> tuple[list[Order], int]:
        """根据用户ID查找订单列表

        Args:
            user_id: 用户ID
            status: 订单状态（可选）
            page: 当前页码（可选，如果提供则启用数据库分页）
            per_page: 每页数量（可选，如果提供则启用数据库分页）

        Returns:
            包含订单聚合根列表和总记录数的元组
        """
        # 构建基础查询
        base_query = select(OrderModel).where(
            OrderModel.user_id == user_id,
            OrderModel.deleted_at == None
        )

        if status:
            base_query = base_query.where(OrderModel.status == status.value)

        # 排序
        base_query = base_query.order_by(OrderModel.created_at.desc())

        # 计算总记录数
        count_query = select(db.func.count()).select_from(base_query.subquery())
        total = self.session.execute(count_query).scalar_one()

        # 应用分页
        if page is not None and per_page is not None:
            base_query = base_query.offset((page - 1) * per_page).limit(per_page)

        # 添加预加载选项并执行查询
        query = base_query.options(
            selectinload(OrderModel.items),  # 预加载 items
            selectinload(OrderModel.sub_orders)  # 预加载 sub_orders
        )

        result = self.session.execute(query)
        order_models = result.scalars().all()

        # 转换为领域模型列表
        orders = [self._to_domain_entity(order) for order in order_models]

        return orders, total

    def find_expired_orders(self) -> list[Order]:
        """查找已过期的待支付订单

        Returns:
            已过期的订单聚合根列表
        """
        # 查询所有待支付且已过期的订单
        now = datetime.now()
        stmt = select(OrderModel).where(
            OrderModel.status == OrderStatus.PENDING.value,
            OrderModel.expire_at < now,
            OrderModel.deleted_at == None
        ).options(
            selectinload(OrderModel.items),  # 预加载 items
            selectinload(OrderModel.sub_orders)  # 预加载 sub_orders
        )
        result = self.session.execute(stmt)
        expired_orders = result.scalars().all()

        # 转换为领域模型列表
        return [self._to_domain_entity(order) for order in expired_orders]

    def update_status(self, order_id: str, status: str) -> bool:
        """更新订单状态"""
        stmt = select(OrderModel).where(
            OrderModel.id == order_id,
            OrderModel.deleted_at == None
        )
        result = self.session.execute(stmt)
        order_model = result.scalar_one_or_none()
        if not order_model:
            return False

        order_model.status = status
        return True

    def exists_by_order_number(self, order_number: str) -> bool:
        """检查订单号是否已存在"""
        result = self.session.execute(
            select(OrderModel).where(
                OrderModel.order_no == order_number,
                OrderModel.deleted_at == None)
        )
        return result.scalar_one_or_none() is not None

    def find_by_order_no(self, order_no: str) -> Optional[Order]:
        """根据订单号查找订单"""
        order_model = self.session.execute(
            select(OrderModel)
            .where(
                OrderModel.order_no == order_no,
                OrderModel.deleted_at == None
            )
        ).scalar_one_or_none()

        if not order_model:
            return None

        # 使用_to_domain_entity方法转换为领域模型
        return self._to_domain_entity(order_model)

    def soft_delete(self, order_id: int) -> bool:
        """软删除订单"""
        stmt = select(OrderModel).where(
            OrderModel.id == order_id,
            OrderModel.deleted_at == None
        )
        result = self.session.execute(stmt)
        order_model = result.scalar_one_or_none()

        if not order_model:
            return False

        # 设置删除时间
        now = datetime.now()
        order_model.deleted_at = now

        # 同时软删除关联的订单项和子订单
        for item in order_model.items:
            item.deleted_at = now

        for sub_order in order_model.sub_orders:
            sub_order.deleted_at = now

        return True

    def restore(self, order_id: int) -> bool:
        """恢复软删除的订单"""
        # 注意这里不过滤deleted_at，因为我们要恢复已删除的订单
        stmt = select(OrderModel).where(OrderModel.id == order_id)
        result = self.session.execute(stmt)
        order_model = result.scalar_one_or_none()

        if not order_model or order_model.deleted_at is None:
            return False

        # 清除删除时间
        order_model.deleted_at = None

        # 同时恢复关联的订单项和子订单
        for item in order_model.items:
            item.deleted_at = None

        for sub_order in order_model.sub_orders:
            sub_order.deleted_at = None

        return True

    def hard_delete(self, order_id: int) -> None:
        """硬删除订单及其子订单、订单项

        Args:
            order_id: 订单ID
        """
        # 查找订单（不管是否已软删除）
        stmt = select(OrderModel).where(OrderModel.id == order_id)
        result = self.session.execute(stmt)
        order_model = result.scalar_one_or_none()

        if not order_model:
            raise OrderNotFoundError(f'订单id {order_id} 不存在')

        # 硬删除订单及其关联的子订单和订单项
        # 由于设置了cascade='all, delete-orphan'，删除订单时会自动删除关联的子订单和订单项
        self.session.delete(order_model)

    def find_by_tracking_number(self, logistics_company: str, tracking_number: str) -> list[Order]:
        """根据物流单号查找订单

        Args:
            logistics_company: 物流公司编码
            tracking_number: 物流单号

        Returns:
            包含订单聚合根的列表，如果不存在则返回空列表
        """
        # 构建查询，通过子订单的物流信息字段查找订单
        stmt = select(OrderModel).join(
            SubOrderModel, SubOrderModel.order_id == OrderModel.id
        ).where(
            SubOrderModel.logistics_company == logistics_company,
            SubOrderModel.tracking_number == tracking_number,
            OrderModel.deleted_at == None
        )

        result = self.session.execute(stmt)
        order_models = result.scalars().all()

        if not order_models:
            return []

        return [self._to_domain_entity(om) for om in order_models]

    def count_sub_orders_by_user_and_status(self, user_id: int, status: SubOrderStatus) -> int:
        """统计用户指定状态的子订单数量"""
        stmt = select(func.count(SubOrderModel.id)).join(OrderModel).where(
            OrderModel.user_id == user_id,
            SubOrderModel.status == status.value,
            OrderModel.deleted_at == None,
            SubOrderModel.deleted_at == None
        )
        result = self.session.execute(stmt).scalar_one()
        return result

    def find_sub_orders_by_status(self, status: SubOrderStatus) -> list:
        """根据状态查找子订单

        Args:
            status: 子订单状态

        Returns:
            子订单列表
        """
        # 构建查询
        query = db.session.query(SubOrderModel).filter(
            SubOrderModel.status == status.value,
            SubOrderModel.deleted_at.is_(None)
        ).order_by(SubOrderModel.id.desc())

        # 执行查询
        sub_order_models = query.all()

        # 转换为领域实体
        sub_orders = []
        for sub_order_model in sub_order_models:
            sub_order = self._to_sub_order_entity(sub_order_model)
            sub_orders.append(sub_order)

        return sub_orders

    def find_paginated_sub_orders_by_status(self, status, page=1, per_page=20):
        """获取指定状态的子订单分页数据

        Args:
            status: 子订单状态枚举值
            page: 页码
            per_page: 每页数量

        Returns:
            (list[SubOrder], int): 子订单列表和总数
        """
        # 构建查询
        query = db.session.query(SubOrderModel).filter(
            SubOrderModel.status == status.value,
            SubOrderModel.deleted_at.is_(None)
        ).order_by(SubOrderModel.id.desc())

        # 执行分页查询
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)

        # 转换为领域实体
        sub_orders = []
        for sub_order_model in paginated.items:
            sub_order = self._to_sub_order_entity(sub_order_model)
            sub_orders.append(sub_order)

        return sub_orders, paginated.total

    def find_paginated_sub_orders_by_multiple_statuses(self, statuses, page=1, per_page=20):
        """获取多个状态的子订单分页数据

        Args:
            statuses: 子订单状态枚举值列表
            page: 页码
            per_page: 每页数量

        Returns:
            (list[SubOrder], int): 子订单列表和总数
        """
        # 将枚举值转换为字符串值列表
        status_values = [status.value for status in statuses]

        # 构建查询
        query = db.session.query(SubOrderModel).filter(
            SubOrderModel.status.in_(status_values),
            SubOrderModel.deleted_at.is_(None)
        ).order_by(SubOrderModel.id.desc())

        # 执行分页查询
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)

        # 转换为领域实体
        sub_orders = []
        for sub_order_model in paginated.items:
            sub_order = self._to_sub_order_entity(sub_order_model)
            sub_orders.append(sub_order)

        return sub_orders, paginated.total

    def find_paginated_sub_orders_by_user_and_multiple_statuses(self, user_id: int, statuses: list[SubOrderStatus],
                                                                page: int = 1, per_page: int = 20):
        """获取指定用户多个状态的子订单分页数据

        Args:
            user_id: 用户ID
            statuses: 子订单状态枚举值列表
            page: 页码
            per_page: 每页数量

        Returns:
            (list[SubOrder], int): 子订单列表和总数
        """
        # 将枚举值转换为字符串值列表
        status_values = [status.value for status in statuses]

        # 构建查询
        query = db.session.query(SubOrderModel).join(OrderModel).filter(
            OrderModel.user_id == user_id,
            SubOrderModel.status.in_(status_values),
            OrderModel.deleted_at.is_(None),
            SubOrderModel.deleted_at.is_(None)
        ).order_by(SubOrderModel.id.desc())

        # 执行分页查询
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)

        # 转换为领域实体
        sub_orders = []
        for sub_order_model in paginated.items:
            sub_order = self._to_sub_order_entity(sub_order_model)
            sub_orders.append(sub_order)

        return sub_orders, paginated.total

    def find_paginated_sub_orders_with_parent_by_user_and_multiple_statuses(self, user_id: int,
                                                                            statuses: list[SubOrderStatus],
                                                                            page: int = 1, per_page: int = 20):
        """获取指定用户多个状态的子订单及其父订单的分页数据

        Args:
            user_id: 用户ID
            statuses: 子订单状态枚举值列表
            page: 页码
            per_page: 每页数量

        Returns:
            (list[tuple[SubOrder, Order]], int): 包含子订单和对应父订单的元组列表，以及总数
        """
        # 将枚举值转换为字符串值列表
        status_values = [status.value for status in statuses]

        # 构建查询，获取子订单和父订单模型
        query = db.session.query(SubOrderModel, OrderModel).join(OrderModel).filter(
            OrderModel.user_id == user_id,
            SubOrderModel.status.in_(status_values),
            OrderModel.deleted_at.is_(None),
            SubOrderModel.deleted_at.is_(None)
        ).order_by(SubOrderModel.id.desc())

        # 执行分页查询
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)

        # 转换为领域实体
        result = []
        for sub_order_model, order_model in paginated.items:
            sub_order = self._to_sub_order_entity(sub_order_model)
            order = self._to_domain_entity(order_model)
            result.append((sub_order, order))

        return result, paginated.total

    def find_paginated_sub_orders_by_query_and_status(self, order_query: str, status: SubOrderStatus, page: int = 1,
                                                      per_page: int = 20):
        """根据订单查询字符串和状态获取子订单分页数据

        Args:
            order_query: 主订单号后四位或完整子订单号
            status: 子订单状态枚举值
            page: 页码
            per_page: 每页数量

        Returns:
            (list[SubOrder], int): 子订单列表和总数
        """
        # 优化查询逻辑：先判断长度，再进行相应查询
        if len(order_query) == 4:  # 如果是4位，认为是主订单号后四位
            # 构建子订单号后缀匹配条件：主订单号后四位-数字
            main_order_suffix_condition = and_(
                func.length(SubOrderModel.order_no) >= 6,  # 至少包含"xxxx-1"
                func.substring(SubOrderModel.order_no, func.length(SubOrderModel.order_no) - 5, 5).like(
                    f"{order_query}-%")
            )

            # 构建完整查询
            query = db.session.query(SubOrderModel).filter(
                main_order_suffix_condition,
                SubOrderModel.status == status.value,
                SubOrderModel.deleted_at.is_(None)
            ).order_by(SubOrderModel.id.desc())

            # 执行所有查询以获取所有匹配记录
            all_matching_sub_orders = query.all()

            # 进一步过滤，确保只匹配主订单号后四位（而不是子订单号中间的四位数字）
            filtered_sub_orders = []
            for model in all_matching_sub_orders:
                parts = model.order_no.split('-')
                if len(parts) == 2 and parts[0][-4:] == order_query:
                    filtered_sub_orders.append(model)

            # 计算总数
            total = len(filtered_sub_orders)

            # 手动分页
            start_idx = (page - 1) * per_page
            end_idx = min(start_idx + per_page, total)
            paginated_models = filtered_sub_orders[start_idx:end_idx] if start_idx < total else []

            # 转换为领域实体
            sub_orders = [self._to_sub_order_entity(model) for model in paginated_models]

            return sub_orders, total
        else:
            # 非4位，执行精确查询
            exact_order_query = db.session.query(SubOrderModel).filter(
                SubOrderModel.order_no == order_query,
                SubOrderModel.status == status.value,
                SubOrderModel.deleted_at.is_(None)
            )

            models = exact_order_query.all()
            if models:
                sub_orders = [self._to_sub_order_entity(model) for model in models]
                return sub_orders, len(sub_orders)

            # 未找到完全匹配记录，返回空结果
            return [], 0

    def find_paginated_sub_orders_by_query_and_multiple_statuses(self, order_query: str, statuses: list[SubOrderStatus],
                                                                 page: int = 1, per_page: int = 20):
        """根据订单查询字符串和多个状态获取子订单分页数据

        Args:
            order_query: 主订单号后四位或完整子订单号
            statuses: 子订单状态枚举值列表
            page: 页码
            per_page: 每页数量

        Returns:
            (list[SubOrder], int): 子订单列表和总数
        """
        # 将枚举值转换为字符串值列表
        status_values = [status.value for status in statuses]

        # 优化查询逻辑：先判断长度，再进行相应查询
        if len(order_query) == 4:  # 如果是4位，认为是主订单号后四位
            # 构建子订单号后缀匹配条件：主订单号后四位-数字
            main_order_suffix_condition = and_(
                func.length(SubOrderModel.order_no) >= 6,  # 至少包含"xxxx-1"
                func.substring(SubOrderModel.order_no, func.length(SubOrderModel.order_no) - 5, 5).like(
                    f"{order_query}-%")
            )

            # 构建完整查询
            query = db.session.query(SubOrderModel).filter(
                main_order_suffix_condition,
                SubOrderModel.status.in_(status_values),
                SubOrderModel.deleted_at.is_(None)
            ).order_by(SubOrderModel.id.desc())

            # 执行所有查询以获取所有匹配记录
            all_matching_sub_orders = query.all()

            # 进一步过滤，确保只匹配主订单号后四位（而不是子订单号中间的四位数字）
            filtered_sub_orders = []
            for model in all_matching_sub_orders:
                parts = model.order_no.split('-')
                if len(parts) == 2 and parts[0][-4:] == order_query:
                    filtered_sub_orders.append(model)

            # 计算总数
            total = len(filtered_sub_orders)

            # 手动分页
            start_idx = (page - 1) * per_page
            end_idx = min(start_idx + per_page, total)
            paginated_models = filtered_sub_orders[start_idx:end_idx] if start_idx < total else []

            # 转换为领域实体
            sub_orders = [self._to_sub_order_entity(model) for model in paginated_models]

            return sub_orders, total
        else:
            # 非4位，执行精确查询
            exact_order_query = db.session.query(SubOrderModel).filter(
                SubOrderModel.order_no == order_query,
                SubOrderModel.status.in_(status_values),
                SubOrderModel.deleted_at.is_(None)
            )

            models = exact_order_query.all()
            if models:
                sub_orders = [self._to_sub_order_entity(model) for model in models]
                return sub_orders, len(sub_orders)

            # 未找到完全匹配记录，返回空结果
            return [], 0

    def _to_sub_order_entity(self, model):
        """将SubOrderModel转换为SubOrder实体对象"""
        sub_order = SubOrder(
            id=model.id,
            order_no=model.order_no,
            order_id=model.order_id,
            sku_id=model.sku_id,
            quantity=model.quantity,
            unit_price=Decimal(str(model.unit_price)),
            actual_payment=Decimal(str(model.actual_payment)),
            status=SubOrderStatus(model.status),
            created_at=model.created_at,
            delivered_at=model.delivered_at,
            completed_at=model.completed_at
        )

        # 从独立字段构建物流信息值对象
        if model.logistics_company and model.tracking_number and model.shipping_time:
            # 从tracking_details中提取坐标
            coordinates = ('0', '0')  # 默认值
            if model.tracking_details:
                # 物流详情是按时间倒序排序的，最新的在前面
                for detail in model.tracking_details:
                    if area_center := detail.get('area_center'):
                        if area_center[0] != '0' and area_center[1] != '0':
                            coordinates = area_center
                            break  # 找到有效坐标后退出循环

            sub_order.logistics_info = LogisticsInfo(
                logistics_company=model.logistics_company,
                tracking_number=model.tracking_number,
                shipping_time=model.shipping_time,
                salt=model.salt,
                area_center=coordinates,
                tracking_details=model.tracking_details
            )

        # 如果有退款信息，解析退款信息
        if model.refund_info:
            sub_order.refund_info = self._to_refund_info(model.refund_info)

        # 如果有售后信息，解析售后信息
        if model.after_sale_info:
            sub_order.after_sale_info = self._dict_to_after_sale_info(model.after_sale_info)

        return sub_order

    @staticmethod
    def _dict_to_operation_logs(data: list[dict[str, Any]]) -> list[OrderOperationLog]:
        result = []
        for log_dict in data:
            log = OrderOperationLog(
                operation_type=log_dict['operation_type'],
                operator_id=log_dict['operator_id'],
                operator_type=log_dict['operator_type'],
                operation_time=datetime.fromisoformat(log_dict['operation_time']),
                operation_detail=log_dict['operation_detail']
            )
            if 'previous_status' in log_dict and log_dict['previous_status']:
                log.previous_status = OrderStatus(log_dict['previous_status'])
            if 'current_status' in log_dict and log_dict['current_status']:
                log.current_status = OrderStatus(log_dict['current_status'])
            if 'previous_sub_status' in log_dict and log_dict['previous_sub_status']:
                log.previous_sub_status = SubOrderStatus(log_dict['previous_sub_status'])
            if 'current_sub_status' in log_dict and log_dict['current_sub_status']:
                log.current_sub_status = SubOrderStatus(log_dict['current_sub_status'])
            result.append(log)
        return result

    def _create_order_model(self, order: Order) -> OrderModel:
        """创建订单数据模型

        Args:
            order: 订单聚合根

        Returns:
            订单数据模型
        """
        # 创建订单模型
        order_model = OrderModel(
            order_no=order.order_no,
            user_id=order.user_id,
            order_type=order.order_type.value,
            status=order.status.value,
            total_amount=order.total_amount,
            original_amount=order.original_amount,
            total_refunded=order.total_refunded if hasattr(order, 'total_refunded') else Decimal('0.00'),
            coupon_ids=order.coupon_ids,
            shipping_address=self._shipping_address_to_dict(order.shipping_address) if order.shipping_address else None,
            remark=order.remark,
            payment_info=self._payment_info_to_dict(order.payment_info) if order.payment_info else None,
            operation_logs=self._operation_logs_to_dict(order.operation_logs) if order.operation_logs else None,
            created_at=order.created_at,
            paid_at=order.paid_at,
            cancelled_at=order.cancelled_at,
            completed_at=order.completed_at,
            expire_at=order.expire_at,
            cancel_reason=order.cancel_reason.value if order.cancel_reason else None,
            version=order.version
        )

        # 创建订单项模型
        for item in order.items:
            item_model = OrderItemModel(
                id=item.id,
                order_id=order.id,
                sku_id=item.sku_id,
                quantity=item.quantity,
                unit_price=item.unit_price,
                original_price=item.original_price,
                postage=item.postage,
                points_ratio=item.points_ratio,
                group_price=item.group_price
            )
            order_model.items.append(item_model)

        # 创建子订单模型
        for sub_order in order.sub_orders:
            sub_order_model = SubOrderModel(
                id=sub_order.id,
                order_no=sub_order.order_no,  # 子订单号
                order_id=order.id,
                sku_id=sub_order.sku_id,
                quantity=sub_order.quantity,
                unit_price=sub_order.unit_price,
                actual_payment=sub_order.actual_payment,
                status=sub_order.status.value,
                logistics_info=self._logistics_info_to_dict(
                    sub_order.logistics_info) if sub_order.logistics_info else None,
                refund_info=self._refund_info_to_dict(sub_order.refund_info) if sub_order.refund_info else None,
                after_sale_info=self._after_sale_info_to_dict(
                    sub_order.after_sale_info) if sub_order.after_sale_info else None,
                created_at=sub_order.created_at,
                delivered_at=sub_order.delivered_at,
                completed_at=sub_order.completed_at
            )
            order_model.sub_orders.append(sub_order_model)

        return order_model

    def _update_order_model(self, order_model: OrderModel, order: Order) -> None:
        """更新订单数据模型

        Args:
            order_model: 订单数据模型
            order: 订单聚合根
        """
        # 更新订单基本信息
        order_model.status = order.status.value
        order_model.total_amount = order.total_amount
        order_model.original_amount = order.original_amount
        order_model.coupon_ids = order.coupon_ids
        order_model.shipping_address = self._shipping_address_to_dict(
            order.shipping_address) if order.shipping_address else None
        order_model.remark = order.remark
        order_model.payment_info = self._payment_info_to_dict(order.payment_info) if order.payment_info else None
        order_model.operation_logs = self._operation_logs_to_dict(
            order.operation_logs) if order.operation_logs else None
        order_model.paid_at = order.paid_at
        order_model.cancelled_at = order.cancelled_at
        order_model.completed_at = order.completed_at
        order_model.expire_at = order.expire_at
        order_model.cancel_reason = order.cancel_reason.value if order.cancel_reason else None
        order_model.version = order.version

        # 获取聚合根中所有子订单的ID集合
        aggregate_sub_order_ids = {sub_order.id for sub_order in order.sub_orders if sub_order.id is not None}

        # 处理需要软删除的子订单 (不在聚合根中的子订单)
        now = datetime.now()
        for sub_order_model in order_model.sub_orders:
            if sub_order_model.id not in aggregate_sub_order_ids and sub_order_model.deleted_at is None:
                sub_order_model.deleted_at = now

        # 处理子订单更新和新增
        sub_order_model_map = {so.id: so for so in order_model.sub_orders}
        for sub_order in order.sub_orders:
            if sub_order.id and (sub_order_model := sub_order_model_map.get(sub_order.id)):
                # 更新现有子订单
                sub_order_model.order_no = sub_order.order_no
                sub_order_model.status = sub_order.status.value
                sub_order_model.actual_payment = sub_order.actual_payment
                sub_order_model.delivered_at = sub_order.delivered_at
                sub_order_model.completed_at = sub_order.completed_at
                # 更新物流信息字段
                if sub_order.logistics_info:
                    sub_order_model.logistics_company = sub_order.logistics_info.logistics_company
                    sub_order_model.tracking_number = sub_order.logistics_info.tracking_number
                    sub_order_model.shipping_time = sub_order.logistics_info.shipping_time
                    sub_order_model.salt = sub_order.logistics_info.salt
                    sub_order_model.tracking_details = sub_order.logistics_info.tracking_details
                else:
                    sub_order_model.logistics_company = None
                    sub_order_model.tracking_number = None
                    sub_order_model.shipping_time = None
                    sub_order_model.salt = None
                    sub_order_model.tracking_details = None
                sub_order_model.refund_info = self._refund_info_to_dict(
                    sub_order.refund_info) if sub_order.refund_info else None
                sub_order_model.after_sale_info = self._after_sale_info_to_dict(
                    sub_order.after_sale_info) if sub_order.after_sale_info else None
            else:
                # 添加新子订单
                new_sub_order = SubOrderModel(
                    order_no=sub_order.order_no,
                    order_id=order.id,
                    sku_id=sub_order.sku_id,
                    quantity=sub_order.quantity,
                    unit_price=sub_order.unit_price,
                    actual_payment=sub_order.actual_payment,
                    status=sub_order.status.value,
                    # 设置物流信息字段
                    logistics_company=sub_order.logistics_info.logistics_company if sub_order.logistics_info else None,
                    tracking_number=sub_order.logistics_info.tracking_number if sub_order.logistics_info else None,
                    shipping_time=sub_order.logistics_info.shipping_time if sub_order.logistics_info else None,
                    salt=sub_order.logistics_info.salt if sub_order.logistics_info else None,
                    tracking_details=sub_order.logistics_info.tracking_details if sub_order.logistics_info else None,
                    refund_info=self._refund_info_to_dict(sub_order.refund_info) if sub_order.refund_info else None,
                    after_sale_info=self._after_sale_info_to_dict(
                        sub_order.after_sale_info) if sub_order.after_sale_info else None,
                    created_at=sub_order.created_at,
                    delivered_at=sub_order.delivered_at,
                    completed_at=sub_order.completed_at
                )
                order_model.sub_orders.append(new_sub_order)

    def _to_domain_entity(self, order_model: OrderModel) -> Order:
        """将数据模型转换为领域实体

        Args:
            order_model: 订单数据模型

        Returns:
            订单聚合根
        """
        # 创建订单聚合根
        order = Order()
        order.id = order_model.id
        order.order_no = order_model.order_no
        order.user_id = order_model.user_id
        order.order_type = OrderType(order_model.order_type)
        order.status = OrderStatus(order_model.status)
        order.total_amount = order_model.total_amount
        order.original_amount = order_model.original_amount
        order.total_refunded = order_model.total_refunded if order_model.total_refunded else Decimal('0.00')
        order.coupon_ids = order_model.coupon_ids if order_model.coupon_ids else []
        order.remark = order_model.remark
        order.created_at = order_model.created_at
        order.paid_at = order_model.paid_at
        order.cancelled_at = order_model.cancelled_at
        order.completed_at = order_model.completed_at
        order.expire_at = order_model.expire_at
        order.cancel_reason = CancelReason(order_model.cancel_reason) if order_model.cancel_reason else None
        order.version = order_model.version

        # 转换收货地址
        if order_model.shipping_address:
            order.shipping_address = self._dict_to_shipping_address(order_model.shipping_address)

        # 转换支付信息
        if order_model.payment_info:
            order.payment_info = self._dict_to_payment_info(order_model.payment_info)

        # 转换操作日志
        if order_model.operation_logs:
            order.operation_logs = self._dict_to_operation_logs(order_model.operation_logs)

        # 转换订单项
        for item_model in order_model.items:
            item = OrderItem(
                id=item_model.id,
                order_id=item_model.order_id,
                sku_id=item_model.sku_id,
                quantity=item_model.quantity,
                unit_price=Decimal(str(item_model.unit_price)),
                original_price=Decimal(str(item_model.original_price)),
                postage=Decimal(str(item_model.postage)),
                points_ratio=item_model.points_ratio,
                group_price=Decimal(str(item_model.group_price))
            )
            order.items.append(item)

        # 转换子订单
        for sub_order_model in order_model.sub_orders:
            sub_order = SubOrder(
                id=sub_order_model.id,
                order_no=sub_order_model.order_no,
                order_id=sub_order_model.order_id,
                sku_id=sub_order_model.sku_id,
                quantity=sub_order_model.quantity,
                unit_price=Decimal(str(sub_order_model.unit_price)),
                actual_payment=Decimal(str(sub_order_model.actual_payment)),
                status=SubOrderStatus(sub_order_model.status),
                created_at=sub_order_model.created_at,
                delivered_at=sub_order_model.delivered_at,
                completed_at=sub_order_model.completed_at
            )
            # 从独立字段构建物流信息值对象
            if sub_order_model.logistics_company and sub_order_model.tracking_number and sub_order_model.shipping_time:
                # 从tracking_details中提取坐标
                coordinates = ('0', '0')  # 默认值
                if sub_order_model.tracking_details:
                    # 物流详情是按时间倒序排序的，最新的在前面
                    for detail in sub_order_model.tracking_details:
                        if isinstance(detail, dict) and 'area_center' in detail:
                            area_center = detail.get('area_center')
                            if area_center[0] != '0' and area_center[1] != '0':
                                coordinates = area_center
                                break  # 找到有效坐标后退出循环

                sub_order.logistics_info = LogisticsInfo(
                    logistics_company=sub_order_model.logistics_company,
                    tracking_number=sub_order_model.tracking_number,
                    shipping_time=sub_order_model.shipping_time,
                    salt=sub_order_model.salt,
                    area_center=coordinates,
                    tracking_details=sub_order_model.tracking_details
                )
            if sub_order_model.refund_info:
                sub_order.refund_info = self._dict_to_refund_info(sub_order_model.refund_info)

            # 如果有售后信息，转换售后信息
            if sub_order_model.after_sale_info:
                sub_order.after_sale_info = self._dict_to_after_sale_info(sub_order_model.after_sale_info)

            order.sub_orders.append(sub_order)

        return order

    # 值对象转换方法
    @staticmethod
    def _shipping_address_to_dict(address: ShippingAddress) -> dict[str, Any]:
        return {
            'province': address.province,
            'city': address.city,
            'district': address.district,
            'detail': address.detail,
            'contact_name': address.contact_name,
            'contact_phone': address.contact_phone
        }

    @staticmethod
    def _dict_to_shipping_address(data: dict[str, Any]) -> ShippingAddress:
        return ShippingAddress(
            province=data['province'],
            city=data['city'],
            district=data['district'],
            detail=data['detail'],
            contact_name=data['contact_name'],
            contact_phone=data['contact_phone']
        )

    @staticmethod
    def _payment_info_to_dict(payment_info: PaymentInfo) -> dict[str, Any]:
        return {
            'payment_id': payment_info.payment_id,
            'payment_method': payment_info.payment_method,
            'payment_amount': str(payment_info.payment_amount),  # 将Decimal转换为字符串以便JSON序列化
            'payment_time': payment_info.payment_time.isoformat(),
            'transaction_id': payment_info.transaction_id
        }

    @staticmethod
    def _dict_to_payment_info(data: dict[str, Any]) -> PaymentInfo:
        return PaymentInfo(
            payment_id=data['payment_id'],
            payment_method=data['payment_method'],
            payment_amount=data['payment_amount'],
            payment_time=datetime.fromisoformat(data['payment_time']),
            transaction_id=data['transaction_id']
        )

    @staticmethod
    def _refund_info_to_dict(refund_info: RefundInfo) -> dict[str, Any]:
        result = {
            'refund_no': refund_info.refund_no,
            'refund_amount': str(refund_info.refund_amount),  # 将Decimal转换为字符串以便JSON序列化
            'refund_reason': refund_info.refund_reason.value,
            'refund_status': refund_info.refund_status.value,
            'refund_channel': refund_info.refund_channel.value,
        }
        if refund_info.refund_time:
            result['refund_time'] = refund_info.refund_time.isoformat()
        if refund_info.refund_transaction_id:
            result['refund_transaction_id'] = refund_info.refund_transaction_id
        if refund_info.user_received_account:
            result['user_received_account'] = refund_info.user_received_account
        return result

    @staticmethod
    def _dict_to_refund_info(data: dict[str, Any]) -> RefundInfo:
        refund_info = RefundInfo(
            refund_no=data['refund_no'],
            refund_transaction_id=data.get('refund_transaction_id', ''),
            refund_amount=data['refund_amount'],
            refund_reason=RefundReason(data['refund_reason']),
            refund_status=RefundStatus(data['refund_status']),
            refund_channel=RefundChannel(data.get('refund_channel', 'original'))
        )
        if 'refund_time' in data and data['refund_time']:
            refund_info.refund_time = datetime.fromisoformat(data['refund_time'])
        if 'user_received_account' in data and data['user_received_account']:
            refund_info.user_received_account = data['user_received_account']
        return refund_info

    @staticmethod
    def _operation_logs_to_dict(logs: list[OrderOperationLog]) -> list[dict[str, Any]]:
        result = []
        for log in logs:
            log_dict = {
                'operation_type': log.operation_type,
                'operator_id': log.operator_id,
                'operator_type': log.operator_type,
                'operation_time': log.operation_time.isoformat(),
                'operation_detail': log.operation_detail
            }
            if log.previous_status:
                log_dict['previous_status'] = log.previous_status.value
            if log.current_status:
                log_dict['current_status'] = log.current_status.value
            if log.previous_sub_status:
                log_dict['previous_sub_status'] = log.previous_sub_status.value
            if log.current_sub_status:
                log_dict['current_sub_status'] = log.current_sub_status.value
            result.append(log_dict)
        return result

    @staticmethod
    def _to_refund_info(refund_data: dict) -> Optional[RefundInfo]:
        """将退款信息字典转换为RefundInfo值对象"""
        if not refund_data:
            return None

        return RefundInfo(
            refund_no=refund_data.get('refund_no'),
            refund_amount=Decimal(str(refund_data.get('refund_amount', '0.00'))),
            refund_reason=RefundReason(refund_data.get('refund_reason')) if refund_data.get('refund_reason') else None,
            refund_status=RefundStatus(refund_data.get('refund_status')) if refund_data.get(
                'refund_status') else RefundStatus.PROCESSING,
            refund_time=datetime.fromisoformat(refund_data.get('refund_time')) if refund_data.get(
                'refund_time') else None,
            user_received_account=refund_data.get('user_received_account')
        )

    @staticmethod
    def _after_sale_info_to_dict(after_sale_info: AfterSaleInfo) -> dict[str, Any]:
        """将售后信息值对象转换为字典"""
        result = {
            'apply_type': after_sale_info.apply_type.value,
            'description': after_sale_info.description,
            'evidence_images': after_sale_info.evidence_images,
            'applied_at': after_sale_info.applied_at.isoformat(),
        }

        # 添加可选字段
        if after_sale_info.process_type:
            result['process_type'] = after_sale_info.process_type.value
        if after_sale_info.process_description:
            result['process_description'] = after_sale_info.process_description
        if after_sale_info.refund_amount:
            result['refund_amount'] = str(after_sale_info.refund_amount)
        if after_sale_info.processed_at:
            result['processed_at'] = after_sale_info.processed_at.isoformat()
        if after_sale_info.processed_by:
            result['processed_by'] = after_sale_info.processed_by

        return result

    @staticmethod
    def _dict_to_after_sale_info(data: dict[str, Any]) -> AfterSaleInfo:
        """将字典转换为售后信息值对象"""
        after_sale_info = AfterSaleInfo(
            apply_type=AfterSaleType(data['apply_type']),
            description=data['description'],
            evidence_images=data.get('evidence_images', []),
            applied_at=datetime.fromisoformat(data['applied_at']) if 'applied_at' in data else datetime.now()
        )

        # 设置可选字段
        if 'process_type' in data and data['process_type']:
            after_sale_info.process_type = AfterSaleProcessType(data['process_type'])
        if 'process_description' in data and data['process_description']:
            after_sale_info.process_description = data['process_description']
        if 'refund_amount' in data and data['refund_amount']:
            after_sale_info.refund_amount = Decimal(str(data['refund_amount']))
        if 'processed_at' in data and data['processed_at']:
            after_sale_info.processed_at = datetime.fromisoformat(data['processed_at'])
        if 'processed_by' in data and data['processed_by']:
            after_sale_info.processed_by = data['processed_by']

        return after_sale_info
