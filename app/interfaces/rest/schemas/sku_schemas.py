from marshmallow import Schema, fields


class UpdateSkuPublishStatusSchema(Schema):
    """更新sku上架/下架状态的参数schema"""
    sku_id = fields.Integer(required=True, data_key="skuId")
    is_published = fields.Boolean(required=True, data_key="isPublished")


class UpdateSkuInfoSchema(Schema):
    """更新sku信息的参数schema"""
    sku_id = fields.Integer(required=True, data_key="skuId")
    price = fields.Decimal(places=2, data_key="price", allow_none=True)
    group_price = fields.Decimal(places=2, data_key="groupPrice", allow_none=True)
    postage = fields.Decimal(places=2, data_key="postage", allow_none=True)
    commission = fields.Decimal(places=2, data_key="commission", allow_none=True)
    actual_stock = fields.Integer(data_key="actualStock", allow_none=True)
