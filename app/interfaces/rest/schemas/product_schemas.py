from marshmallow import Schema, fields, validates_schema
from marshmallow.validate import OneOf, Range
from app.utils.errors import ValidationError


class ProductDetailQuerySchema(Schema):
    """查询商品详情的参数schema"""
    product_id = fields.Integer(required=True, data_key="productId")


class SKUQueryByProductSchema(Schema):
    """通过商品ID查询SKU的参数schema"""
    product_id = fields.Int(required=True)


class SKUDetailSchema(Schema):
    """查询SKU详情的参数schema"""
    sku_id = fields.Integer(required=True, data_key="skuId")


class GetProductCommissionSchema(Schema):
    """获取商品佣金的参数schema"""
    product_id = fields.Integer(required=True, data_key="productId")


class ProductTypeSchema(Schema):
    """商品类型查询参数schema"""
    type_ = fields.String(required=True, validate=OneOf(['trace', 'wiki']), data_key="type")


class GetProductsByCategorySchema(Schema):
    """获取分类商品列表的参数schema"""
    category_id = fields.Integer(required=True, data_key="categoryId")


class GetProductsByGroupSchema(Schema):
    """获取分组商品列表的参数schema"""
    group_id = fields.Integer(required=True, data_key="groupId")


class GetProductsByChannelSchema(Schema):
    """获取频道商品列表的查询参数"""
    channel_id = fields.Integer(required=True, data_key="channelId", validate=Range(min=1))


class GetProductsByRegionSchema(Schema):
    """获取地域商品列表的请求参数"""
    region_id = fields.Integer(required=True, data_key="regionId", validate=Range(min=1))


class UpdateProductPublishStatusSchema(Schema):
    """更新商品上架/下架状态的参数schema"""
    product_id = fields.Integer(required=True, data_key="productId")
    is_published = fields.Boolean(required=True, data_key="isPublished")
