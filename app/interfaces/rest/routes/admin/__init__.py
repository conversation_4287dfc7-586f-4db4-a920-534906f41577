from . import user_admin_routes, group_admin_routes, carousel_admin_routes, order_admin_routes, \
    shipping_location_routes, commission_admin_routes, me_admin_routes, statistics_admin_routes, \
    bill_admin_routes, sku_admin_routes


def register_admin_routes(app):
    """注册所有管理员路由"""
    app.register_blueprint(user_admin_routes.bp)
    app.register_blueprint(group_admin_routes.bp)
    app.register_blueprint(carousel_admin_routes.bp)
    app.register_blueprint(order_admin_routes.bp)
    app.register_blueprint(shipping_location_routes.bp)
    app.register_blueprint(commission_admin_routes.bp)
    app.register_blueprint(me_admin_routes.bp)
    app.register_blueprint(statistics_admin_routes.bp)
    app.register_blueprint(bill_admin_routes.bp)
    app.register_blueprint(sku_admin_routes.bp)
