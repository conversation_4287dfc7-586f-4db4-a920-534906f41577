from flask import Blueprint

from app.application.product.use_cases.update_sku_info_use_case import update_sku_info_use_case
from app.application.product.use_cases.update_sku_publish_status_use_case import update_sku_publish_status_use_case
from app.constants import ErrorCode
from app.decorators.auth import admin_required
from app.decorators.validation import validate_with
from app.interfaces.rest.schemas.sku_schemas import UpdateSkuPublishStatusSchema, UpdateSkuInfoSchema

bp = Blueprint('admin_sku', __name__, url_prefix='/api/admin/skus')


@bp.put('/publish-status')
@admin_required
@validate_with(UpdateSkuPublishStatusSchema)
def update_sku_publish_status(sku_id, is_published):
    """管理员更新sku上架/下架状态

    根据传入的sku_id和上架状态，更新sku的上架/下架状态
    如果sku当前状态已经是目标状态，则返回错误码1
    如果下架SKU后，商品没有其他有效SKU，则同时下架商品
    """
    result = update_sku_publish_status_use_case(sku_id, is_published)
    return result


@bp.patch('/info')
@admin_required
@validate_with(UpdateSkuInfoSchema)
def update_sku_info(sku_id, price=None, group_price=None, postage=None, commission=None, actual_stock=None):
    """管理员更新SKU信息

    根据传入的sku_id和相关参数，更新SKU的价格、库存等信息
    支持单独或组合更新：原价、团员价、邮费、佣金、库存
    """
    result = update_sku_info_use_case(
        sku_id=sku_id,
        price=price,
        group_price=group_price,
        postage=postage,
        commission=commission,
        actual_stock=actual_stock
    )
    return {
        'code': ErrorCode.SUCCESS.code,
        'data': result
    }
