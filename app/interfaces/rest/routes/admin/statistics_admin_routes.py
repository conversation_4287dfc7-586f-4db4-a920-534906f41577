from datetime import datetime, timedelta
from decimal import Decimal

from flask import Blueprint, request, current_app
from sqlalchemy import select, func

from app.constants import ErrorCode
from app.decorators.auth import admin_required
from app.domain.order.value_objects import SubOrderStatus
from app.extensions import db
from app.infrastructure.order.models import OrderModel, SubOrderModel
from app.models import SKU as SkuModel, Product
from app.models.group import GroupMember, GroupLeaderInfo
from app.models.user import GroupLeaderCommissionDetail, User

bp = Blueprint('statistics_admin', __name__, url_prefix='/api/admin/statistics')


@bp.get('/sales-summary')
@admin_required
def get_sales_summary():
    """获取管理员销售数据统计

    接收时间参数，包含今天、昨天、近七天、本月（按自然月算）四种之一
    返回以下统计数据：
    - 销售额：所有订单总金额
    - 成交额：已支付、已发货、已送达、已完成、售后已拒绝、售后待处理，以及售后部分退款的未退款部分的订单总金额
    - 成交率：成交额/销售额
    - 退款金额：退款订单总金额
    - 总单量：所有订单数量
    - 成交单量：已支付、已发货、已送达、已完成、售后已拒绝、售后待处理，以及售后部分退款的订单数量
    - 完成单量：已完成订单数量
    - 售后单量：售后相关订单数量
    - 退款单量：退款相关订单数量
    - 团员单量：团员订单数量
    - 非团员单量：非团员订单数量
    - 总计佣金：所有应该发放给团长的佣金总额
    - 待发放佣金：属于团长但还未发放到团长账户的佣金
    - 已发放佣金：已经发放到团长账户的佣金
    """
    # 获取时间参数
    time_range = request.args.get('time_range', 'today')

    # 根据时间参数计算查询时间范围
    start_date, end_date = get_date_range(time_range)

    # 初始化统计数据
    stats = {
        "sales_amount": Decimal('0.00'),  # 销售额
        "transaction_amount": Decimal('0.00'),  # 成交额
        "transaction_rate": "0%",  # 成交率
        "refund_amount": Decimal('0.00'),  # 退款金额
        "total_orders": 0,  # 总单量
        "transaction_orders": 0,  # 成交单量
        "completed_orders": 0,  # 完成单量
        "after_sale_orders": 0,  # 售后单量
        "refund_orders": 0,  # 退款单量
        "group_member_orders": 0,  # 团员单量
        "non_group_member_orders": 0,  # 非团员单量
        "total_commission": Decimal('0.00'),  # 总计佣金
        "pending_commission": Decimal('0.00'),  # 待结算佣金
        "settled_commission": Decimal('0.00')  # 已结算佣金
    }

    # 查询订单数据
    # 1. 销售额统计（所有已支付订单）
    sales_amount_result = db.session.execute(
        select(func.sum(SubOrderModel.actual_payment))
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(start_date, end_date),
            OrderModel.paid_at.isnot(None)
        )
    ).scalar_one_or_none()

    stats["sales_amount"] = sales_amount_result or Decimal('0.00')

    # 2. 成交额统计（已支付、已发货、已送达、已完成、售后已拒绝、售后待处理，以及售后部分退款的未退款部分）
    # 定义成交额统计的状态列表
    transaction_statuses = [
        SubOrderStatus.PAID.value,
        SubOrderStatus.SHIPPED.value,
        SubOrderStatus.DELIVERED.value,
        SubOrderStatus.COMPLETED.value,
        SubOrderStatus.AFTER_SALE_REJECTED.value,
        SubOrderStatus.AFTER_SALE_PENDING.value
    ]

    # 查询基本成交额（不包含部分退款的订单）
    transaction_amount_result = db.session.execute(
        select(func.sum(SubOrderModel.actual_payment))
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(start_date, end_date),
            OrderModel.paid_at.isnot(None),
            SubOrderModel.status.in_(transaction_statuses)
        )
    ).scalar_one_or_none()

    # 处理售后部分退款的订单（只计算未退款部分）
    # 由于退款信息存储在JSON字段中，我们需要单独查询这些订单并处理
    partial_refund_orders = db.session.execute(
        select(SubOrderModel)
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(start_date, end_date),
            OrderModel.paid_at.isnot(None),
            SubOrderModel.status == SubOrderStatus.AFTER_SALE_PARTIAL_REFUNDED.value
        )
    ).scalars().all()

    # 计算部分退款订单的未退款金额
    partial_refund_amount = Decimal('0.00')
    for order in partial_refund_orders:
        # 获取订单实际支付金额
        actual_payment = order.actual_payment or Decimal('0.00')

        # 从refund_info JSON字段中获取退款金额
        refund_amount = Decimal('0.00')
        if order.refund_info and isinstance(order.refund_info, dict) and 'refund_amount' in order.refund_info:
            try:
                refund_amount = Decimal(str(order.refund_info['refund_amount']))
            except (ValueError, TypeError, KeyError):
                # 如果转换失败，使用0
                pass

        # 计算未退款部分并累加
        non_refunded_amount = actual_payment - refund_amount
        if non_refunded_amount > 0:
            partial_refund_amount += non_refunded_amount

    # 将基本成交额和部分退款订单的未退款部分相加得到总成交额
    stats["transaction_amount"] = (transaction_amount_result or Decimal('0.00')) + partial_refund_amount

    # 3. 成交率计算
    if stats["sales_amount"] > 0:
        transaction_rate = (stats["transaction_amount"] / stats["sales_amount"]) * 100
        stats["transaction_rate"] = f"{transaction_rate:.2f}%"
    else:
        stats["transaction_rate"] = "0.00%"

    # 4. 退款金额和退款单量统计 - 只计算退款成功的子订单
    # 扩大查询时间范围，开始时间向前推3天，以捕获可能在查询时间范围内完成退款的订单
    extended_start_date = start_date - timedelta(days=3)

    # 查询可能的退款订单（包括退款成功状态的订单）
    potential_refund_orders = db.session.execute(
        select(SubOrderModel)
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(extended_start_date, end_date),
            OrderModel.paid_at.isnot(None),
            SubOrderModel.status == SubOrderStatus.REFUNDED.value  # 只查询退款成功的订单
        )
    ).scalars().all()

    # 在内存中根据退款成功时间过滤并同时计算退款金额和退款单量
    refund_amount = Decimal('0.00')
    refund_orders_count = 0
    for order in potential_refund_orders:
        # 从refund_info JSON字段中获取退款成功时间
        refund_time = None
        if order.refund_info and isinstance(order.refund_info, dict) and 'refund_time' in order.refund_info:
            try:
                # 尝试将字符串转换为datetime对象
                refund_time_str = order.refund_info['refund_time']
                if isinstance(refund_time_str, str):
                    refund_time = datetime.fromisoformat(refund_time_str.replace('Z', '+00:00'))
                elif isinstance(refund_time_str, datetime):
                    refund_time = refund_time_str
            except (ValueError, TypeError, KeyError):
                # 如果转换失败，继续处理下一个订单
                continue

        # 如果退款成功时间在查询时间范围内，同时累加退款金额和退款单量
        if refund_time and start_date <= refund_time <= end_date:
            refund_amount += order.actual_payment or Decimal('0.00')
            refund_orders_count += 1

    stats["refund_amount"] = refund_amount
    stats["refund_orders"] = refund_orders_count

    # 5. 总单量统计
    total_orders_result = db.session.execute(
        select(func.count(SubOrderModel.id))
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(start_date, end_date),
            OrderModel.paid_at.isnot(None)
        )
    ).scalar_one_or_none()

    stats["total_orders"] = total_orders_result or 0

    # 6. 成交单量统计（已支付、已发货、已送达、已完成、售后已拒绝、售后待处理，以及售后部分退款的订单）
    transaction_orders_result = db.session.execute(
        select(func.count(SubOrderModel.id))
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(start_date, end_date),
            OrderModel.paid_at.isnot(None),
            SubOrderModel.status.in_(transaction_statuses + [SubOrderStatus.AFTER_SALE_PARTIAL_REFUNDED.value])
        )
    ).scalar_one_or_none()

    # 已完成订单单量统计（仅已完成状态，以完成时间为准）
    completed_orders_result = db.session.execute(
        select(func.count(SubOrderModel.id))
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            SubOrderModel.completed_at.between(start_date, end_date),
            SubOrderModel.completed_at.isnot(None),
            SubOrderModel.status == SubOrderStatus.COMPLETED.value
        )
    ).scalar_one_or_none()

    stats["transaction_orders"] = transaction_orders_result or 0
    stats["completed_orders"] = completed_orders_result or 0  # 完成单量仅统计已完成状态

    # 7. 售后单量统计 - 使用售后申请时间为准
    after_sale_statuses = [
        SubOrderStatus.AFTER_SALE_PENDING.value,
        SubOrderStatus.AFTER_SALE_FULL_REFUNDED.value,
        SubOrderStatus.AFTER_SALE_PARTIAL_REFUNDED.value,
        SubOrderStatus.AFTER_SALE_REJECTED.value
    ]

    # 扩大查询时间范围，开始时间向前推15天，以捕获可能在查询时间范围内申请售后的订单
    after_sale_extended_start_date = start_date - timedelta(days=15)

    # 查询可能的售后订单
    potential_after_sale_orders = db.session.execute(
        select(SubOrderModel)
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(after_sale_extended_start_date, end_date),
            OrderModel.paid_at.isnot(None),
            SubOrderModel.status.in_(after_sale_statuses)
        )
    ).scalars().all()

    # 在内存中根据售后申请时间过滤并计算售后订单数量
    after_sale_orders_count = 0
    for order in potential_after_sale_orders:
        # 从after_sale_info JSON字段中获取申请时间
        applied_at = None
        if order.after_sale_info and isinstance(order.after_sale_info, dict) and 'applied_at' in order.after_sale_info:
            try:
                # 尝试将字符串转换为datetime对象
                applied_at_str = order.after_sale_info['applied_at']
                if isinstance(applied_at_str, str):
                    applied_at = datetime.fromisoformat(applied_at_str.replace('Z', '+00:00'))
                elif isinstance(applied_at_str, datetime):
                    applied_at = applied_at_str
            except (ValueError, TypeError, KeyError):
                # 如果转换失败，继续处理下一个订单
                continue

        # 如果申请时间在查询时间范围内，累加售后订单数量
        if applied_at and start_date <= applied_at <= end_date:
            after_sale_orders_count += 1

    stats["after_sale_orders"] = after_sale_orders_count

    # 8. 团员单量和非团员单量统计
    # 使用子查询直接在数据库中统计团员订单量，避免将所有团员ID加载到内存中
    group_member_orders_result = db.session.execute(
        select(func.count(SubOrderModel.id))
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(start_date, end_date),
            OrderModel.paid_at.isnot(None),
            OrderModel.user_id.in_(
                select(GroupMember.member_id)
            )
        )
    ).scalar_one_or_none()

    stats["group_member_orders"] = group_member_orders_result or 0

    # 非团员订单量 = 总单量 - 团员单量
    stats["non_group_member_orders"] = stats["total_orders"] - stats["group_member_orders"]

    # 9. 佣金统计
    # 已发放佣金 - 已经进入团长账户的佣金（已记录在佣金明细表中的佣金）
    distributed_commission_result = db.session.execute(
        select(func.sum(GroupLeaderCommissionDetail.amount))
        .where(
            GroupLeaderCommissionDetail.operated_at.between(start_date, end_date),
            GroupLeaderCommissionDetail.operation_type == 'sub_order',
            GroupLeaderCommissionDetail.amount > 0
        )
    ).scalar_one_or_none()

    stats["settled_commission"] = distributed_commission_result or Decimal('0.00')

    # 待发放佣金 - 属于团长但还未发放的佣金
    # 查询所有团员的待结算订单
    pending_commission = Decimal('0.00')

    # 获取所有团长的团员ID列表
    member_ids_query = select(GroupMember.member_id)
    member_ids = db.session.scalars(member_ids_query).all()

    if member_ids:
        # 获取所有待结算子订单并一次性查询相关SKU的佣金
        pending_results = db.session.execute(
            select(SubOrderModel.sku_id, SubOrderModel.quantity, SubOrderModel.order_id)
            .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
            .where(
                OrderModel.user_id.in_(member_ids),
                OrderModel.paid_at.between(start_date, end_date),
                # 已付款、已发货、已送达的都算待结算
                SubOrderModel.status.in_([
                    SubOrderStatus.PAID.value,
                    SubOrderStatus.SHIPPED.value,
                    SubOrderStatus.DELIVERED.value
                ])
            )
        ).all()

        # 获取所有相关SKU的佣金信息
        if pending_results:
            sku_ids = [result.sku_id for result in pending_results]
            from app.models import SKU as SkuModel

            sku_commissions = {}
            sku_commission_results = db.session.execute(
                select(SkuModel.id, SkuModel.commission)
                .where(SkuModel.id.in_(sku_ids))
            ).all()

            for result in sku_commission_results:
                sku_commissions[result.id] = result.commission or Decimal('0.00')

            # 计算预计佣金
            for result in pending_results:
                sku_commission = sku_commissions.get(result.sku_id, Decimal('0.00'))
                # 累加预计佣金
                pending_commission += sku_commission * Decimal(result.quantity)

    stats["pending_commission"] = pending_commission
    # 总计佣金 = 已发放佣金 + 待发放佣金
    stats["total_commission"] = stats["settled_commission"] + stats["pending_commission"]

    # 格式化金额为字符串
    stats["sales_amount"] = str(stats["sales_amount"])
    stats["transaction_amount"] = str(stats["transaction_amount"])
    stats["refund_amount"] = str(stats["refund_amount"])
    stats["total_commission"] = str(stats["total_commission"])
    stats["pending_commission"] = str(stats["pending_commission"])
    stats["settled_commission"] = str(stats["settled_commission"])

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": stats
    }


def get_date_range(time_range):
    """根据时间范围参数获取开始和结束日期"""
    now = datetime.now()
    today_start = datetime(now.year, now.month, now.day, 0, 0, 0)

    if time_range == 'today':
        # 今天: 当天0点到现在
        return today_start, now

    elif time_range == 'yesterday':
        # 昨天: 昨天0点到昨天23:59:59
        yesterday_start = today_start - timedelta(days=1)
        yesterday_end = today_start - timedelta(seconds=1)
        return yesterday_start, yesterday_end

    elif time_range == 'last7days':
        # 近七天: 7天前的0点到现在
        last_week_start = today_start - timedelta(days=6)
        return last_week_start, now

    elif time_range == 'this_month':
        # 本月: 本月1号0点到现在
        month_start = datetime(now.year, now.month, 1, 0, 0, 0)
        return month_start, now

    else:
        # 默认返回今天
        return today_start, now


@bp.get('/transaction-details')
@admin_required
def get_transaction_details():
    """获取指定日期范围内的交易流水明细
    
    接收开始日期和结束日期参数（包含开始日期当天和结束日期当天）
    返回以下数据：
    - 交易明细列表，包含子订单号、商品名、sku名、来源（支付、发货前退款、售后全额退款、售后部分退款）、金额
    - 总笔数
    - 总收入（支付减去退款）
    """
    # 获取日期参数
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    if not start_date_str or not end_date_str:
        return {
            "code": ErrorCode.VALIDATION_ERROR.code,
            "message": "请提供开始日期和结束日期参数"
        }

    try:
        # 解析日期参数，设置时间为当天的开始和结束
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').replace(hour=0, minute=0, second=0)
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').replace(hour=23, minute=59, second=59)

        # 检查日期范围是否超过一年
        date_diff = (end_date - start_date).days
        if date_diff > 366:
            return {
                "code": ErrorCode.VALIDATION_ERROR.code,
                "message": "查询时间范围不能超过一年"
            }
    except ValueError:
        return {
            "code": ErrorCode.VALIDATION_ERROR.code,
            "message": "日期格式错误，请使用YYYY-MM-DD格式"
        }

    # 初始化结果数据
    result = {
        "items": [],
        "income_count": 0,  # 入账笔数
        "expense_count": 0,  # 出账笔数
        "income_amount": Decimal('0.00'),  # 入账额
        "expense_amount": Decimal('0.00')  # 出账额
    }

    # 1. 查询支付成功的订单
    payment_orders = db.session.execute(
        select(SubOrderModel, OrderModel)
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(start_date, end_date),
            OrderModel.paid_at.isnot(None)
        )
    ).all()

    # 获取所有SKU ID，用于一次性查询SKU信息
    sku_ids = [order[0].sku_id for order in payment_orders]

    # 查询SKU信息
    sku_info = {}
    if sku_ids:
        sku_results = db.session.execute(
            select(SkuModel, Product.title)
            .join(Product, Product.id == SkuModel.product_id)
            .where(SkuModel.id.in_(sku_ids))
        ).all()

        for sku, product_title in sku_results:
            sku_info[sku.id] = {
                'sku_title': sku.display_title,
                'product_title': product_title
            }

    # 处理支付订单
    total_payment = Decimal('0.00')
    for sub_order, order in payment_orders:
        sku_data = sku_info.get(sub_order.sku_id, {'sku_title': '未知SKU', 'product_title': '未知商品'})

        # 添加到交易明细列表
        result["items"].append({
            "sub_order_no": sub_order.order_no,
            "product_title": sku_data['product_title'],
            "sku_title": sku_data['sku_title'],
            "source": "paid",
            "amount": str(sub_order.actual_payment or Decimal('0.00')),
            "time": order.paid_at.strftime('%Y-%m-%d %H:%M:%S')  # 支付项使用支付成功时间
        })

        # 累加入账金额和笔数
        payment_amount = sub_order.actual_payment or Decimal('0.00')
        result["income_amount"] += payment_amount
        result["income_count"] += 1

    # 2. 查询退款记录
    # 扩大查询时间范围，开始时间向前推15天，以捕获可能在查询时间范围内完成退款的订单
    extended_start_date = start_date - timedelta(days=15)

    # 查询可能的退款订单
    refund_statuses = [
        SubOrderStatus.REFUNDED.value,  # 发货前退款
        SubOrderStatus.AFTER_SALE_FULL_REFUNDED.value,  # 售后全额退款
        SubOrderStatus.AFTER_SALE_PARTIAL_REFUNDED.value  # 售后部分退款
    ]

    potential_refund_orders = db.session.execute(
        select(SubOrderModel, OrderModel)
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.between(extended_start_date, end_date),
            OrderModel.paid_at.isnot(None),
            SubOrderModel.status.in_(refund_statuses)
        )
    ).all()

    # 获取退款订单的SKU ID
    refund_sku_ids = [order[0].sku_id for order in potential_refund_orders]

    # 查询退款订单的SKU信息
    if refund_sku_ids:
        refund_sku_results = db.session.execute(
            select(SkuModel, Product.title)
            .join(Product, Product.id == SkuModel.product_id)
            .where(SkuModel.id.in_(refund_sku_ids))
        ).all()

        for sku, product_title in refund_sku_results:
            if sku.id not in sku_info:
                sku_info[sku.id] = {
                    'sku_title': sku.display_title,
                    'product_title': product_title
                }

    # 处理退款订单
    total_refund = Decimal('0.00')
    for sub_order, order in potential_refund_orders:
        # 确定退款来源
        if sub_order.status == SubOrderStatus.REFUNDED.value:
            source = sub_order.status
            # 从refund_info JSON字段中获取退款成功时间
            refund_time = None
            refund_amount = sub_order.actual_payment or Decimal('0.00')

            if sub_order.refund_info and isinstance(sub_order.refund_info,
                                                    dict) and 'refund_time' in sub_order.refund_info:
                try:
                    # 尝试将字符串转换为datetime对象
                    refund_time_str = sub_order.refund_info['refund_time']
                    if isinstance(refund_time_str, str):
                        refund_time = datetime.fromisoformat(refund_time_str.replace('Z', '+00:00'))
                    elif isinstance(refund_time_str, datetime):
                        refund_time = refund_time_str
                except (ValueError, TypeError, KeyError):
                    # 如果转换失败，跳过此订单
                    continue

            # 如果退款时间不在查询范围内，跳过
            if not refund_time or not (start_date <= refund_time <= end_date):
                continue

        elif sub_order.status == SubOrderStatus.AFTER_SALE_FULL_REFUNDED.value:
            source = sub_order.status
            # 从refund_info JSON字段中获取退款成功时间
            refund_time = None
            refund_amount = sub_order.actual_payment or Decimal('0.00')

            if sub_order.refund_info and isinstance(sub_order.refund_info,
                                                    dict) and 'refund_time' in sub_order.refund_info:
                try:
                    # 尝试将字符串转换为datetime对象
                    refund_time_str = sub_order.refund_info['refund_time']
                    if isinstance(refund_time_str, str):
                        refund_time = datetime.fromisoformat(refund_time_str.replace('Z', '+00:00'))
                    elif isinstance(refund_time_str, datetime):
                        refund_time = refund_time_str
                except (ValueError, TypeError, KeyError):
                    # 如果转换失败，跳过此订单
                    continue

            # 如果退款时间不在查询范围内，跳过
            if not refund_time or not (start_date <= refund_time <= end_date):
                continue

        elif sub_order.status == SubOrderStatus.AFTER_SALE_PARTIAL_REFUNDED.value:
            source = sub_order.status
            # 从refund_info JSON字段中获取退款金额和退款时间
            refund_time = None
            refund_amount = Decimal('0.00')

            if sub_order.refund_info and isinstance(sub_order.refund_info, dict):
                # 获取退款金额
                if 'refund_amount' in sub_order.refund_info:
                    try:
                        refund_amount = Decimal(str(sub_order.refund_info['refund_amount']))
                    except (ValueError, TypeError):
                        # 如果转换失败，使用0
                        refund_amount = Decimal('0.00')

                # 获取退款时间
                if 'refund_time' in sub_order.refund_info:
                    try:
                        refund_time_str = sub_order.refund_info['refund_time']
                        if isinstance(refund_time_str, str):
                            refund_time = datetime.fromisoformat(refund_time_str.replace('Z', '+00:00'))
                        elif isinstance(refund_time_str, datetime):
                            refund_time = refund_time_str
                    except (ValueError, TypeError):
                        # 如果转换失败，跳过此订单
                        continue

            # 如果退款时间不在查询范围内，跳过
            if not refund_time or not (start_date <= refund_time <= end_date):
                continue
        else:
            # 不应该到达这里，但为了安全起见
            continue

        sku_data = sku_info.get(sub_order.sku_id, {'sku_title': '未知SKU', 'product_title': '未知商品'})

        # 添加到交易明细列表
        result["items"].append({
            "sub_order_no": sub_order.order_no,
            "product_title": sku_data['product_title'],
            "sku_title": sku_data['sku_title'],
            "source": source,
            "amount": str(refund_amount),
            "time": refund_time.strftime('%Y-%m-%d %H:%M:%S')  # 退款项使用退款成功时间
        })

        # 累加出账金额和笔数
        result["expense_amount"] += refund_amount
        result["expense_count"] += 1

    # 格式化金额为字符串
    result["income_amount"] = str(result["income_amount"])
    result["expense_amount"] = str(result["expense_amount"])

    # 按时间倒序排序
    result["items"].sort(key=lambda x: datetime.strptime(x["time"], '%Y-%m-%d %H:%M:%S'), reverse=True)

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": result
    }


@bp.get('/leaders-summary')
@admin_required
def get_group_leaders_audit():
    """获取团长审计数据
    
    返回以下统计数据：
    - 团长总数
    - 成交量（所有时间）
    - 成交额（所有时间）
    - 总佣金（所有时间）
    
    以及团长列表，包含：
    - 团长昵称
    - 注册团长时间
    - 推荐码
    - 成交量
    - 成交额
    - 已发放佣金
    - 待发放佣金
    """
    # 初始化统计数据
    stats = {
        "total_leaders": 0,  # 团长总数
        "total_orders": 0,  # 总成交量
        "total_amount": Decimal('0.00'),  # 总成交额
        "total_commission": Decimal('0.00'),  # 总佣金
        "leaders": []  # 团长列表
    }

    # 1. 查询团长总数
    total_leaders_result = db.session.execute(
        select(func.count(GroupLeaderInfo.id))
    ).scalar_one_or_none()

    stats["total_leaders"] = total_leaders_result or 0

    # 2. 定义成交额统计的状态列表（与get_sales_summary相同）
    transaction_statuses = [
        SubOrderStatus.PAID.value,
        SubOrderStatus.SHIPPED.value,
        SubOrderStatus.DELIVERED.value,
        SubOrderStatus.COMPLETED.value,
        SubOrderStatus.AFTER_SALE_REJECTED.value,
        SubOrderStatus.AFTER_SALE_PENDING.value
    ]

    # 3. 查询总成交量和总成交额
    # 3.1 查询基本成交额（不包含部分退款的订单）
    transaction_stats = db.session.execute(
        select(
            func.count(SubOrderModel.id).label('total_orders'),
            func.sum(SubOrderModel.actual_payment).label('total_amount')
        )
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.isnot(None),
            SubOrderModel.status.in_(transaction_statuses),
            OrderModel.user_id.in_(
                select(GroupMember.member_id)
            )
        )
    ).one_or_none()

    # 初始化成交量和基本成交额
    total_orders = transaction_stats.total_orders or 0 if transaction_stats else 0
    total_amount = transaction_stats.total_amount or Decimal('0.00') if transaction_stats else Decimal('0.00')

    # 3.2 处理售后部分退款的订单（只计算未退款部分）
    # 查询部分退款订单
    partial_refund_orders = db.session.execute(
        select(SubOrderModel)
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.isnot(None),
            SubOrderModel.status == SubOrderStatus.AFTER_SALE_PARTIAL_REFUNDED.value,
            OrderModel.user_id.in_(
                select(GroupMember.member_id)
            )
        )
    ).scalars().all()

    # 计算部分退款订单的未退款金额
    partial_refund_amount = Decimal('0.00')
    for order in partial_refund_orders:
        # 获取订单实际支付金额
        actual_payment = order.actual_payment or Decimal('0.00')

        # 从refund_info JSON字段中获取退款金额
        refund_amount = Decimal('0.00')
        if order.refund_info and isinstance(order.refund_info, dict) and 'refund_amount' in order.refund_info:
            try:
                refund_amount = Decimal(str(order.refund_info['refund_amount']))
            except (ValueError, TypeError, KeyError):
                # 如果转换失败，使用0
                pass

        # 计算未退款部分并累加
        non_refunded_amount = actual_payment - refund_amount
        if non_refunded_amount > 0:
            partial_refund_amount += non_refunded_amount

    # 3.3 计算部分退款订单数量
    partial_refund_orders_count = db.session.execute(
        select(func.count(SubOrderModel.id))
        .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
        .where(
            OrderModel.paid_at.isnot(None),
            SubOrderModel.status == SubOrderStatus.AFTER_SALE_PARTIAL_REFUNDED.value,
            OrderModel.user_id.in_(
                select(GroupMember.member_id)
            )
        )
    ).scalar_one_or_none() or 0

    # 3.4 更新总成交量和总成交额
    total_orders += partial_refund_orders_count
    total_amount += partial_refund_amount

    stats["total_orders"] = total_orders
    stats["total_amount"] = total_amount

    # 4. 查询总佣金（已发放 + 待发放）
    # 4.1 已发放佣金
    settled_commission_result = db.session.execute(
        select(func.sum(GroupLeaderCommissionDetail.amount))
        .where(
            GroupLeaderCommissionDetail.operation_type == 'sub_order',
            GroupLeaderCommissionDetail.amount > 0
        )
    ).scalar_one_or_none()

    settled_commission = settled_commission_result or Decimal('0.00')

    # 4.2 待发放佣金
    pending_commission = Decimal('0.00')

    # 获取所有团员ID列表
    member_ids_query = select(GroupMember.member_id)
    member_ids = db.session.scalars(member_ids_query).all()

    if member_ids:
        # 获取所有待结算子订单并一次性查询相关SKU的佣金
        pending_results = db.session.execute(
            select(SubOrderModel.sku_id, SubOrderModel.quantity, SubOrderModel.order_id)
            .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
            .where(
                OrderModel.user_id.in_(member_ids),
                OrderModel.paid_at.isnot(None),
                # 已付款、已发货、已送达的都算待结算
                SubOrderModel.status.in_([
                    SubOrderStatus.PAID.value,
                    SubOrderStatus.SHIPPED.value,
                    SubOrderStatus.DELIVERED.value
                ])
            )
        ).all()

        # 获取所有相关SKU的佣金信息
        if pending_results:
            sku_ids = [result.sku_id for result in pending_results]
            from app.models import SKU as SkuModel

            sku_commissions = {}
            sku_commission_results = db.session.execute(
                select(SkuModel.id, SkuModel.commission)
                .where(SkuModel.id.in_(sku_ids))
            ).all()

            for result in sku_commission_results:
                sku_commissions[result.id] = result.commission or Decimal('0.00')

            # 计算预计佣金
            for result in pending_results:
                sku_commission = sku_commissions.get(result.sku_id, Decimal('0.00'))
                # 累加预计佣金
                pending_commission += sku_commission * Decimal(result.quantity)

    # 总佣金 = 已发放佣金 + 待发放佣金
    stats["total_commission"] = settled_commission + pending_commission

    # 5. 查询所有团长信息
    leaders_query = db.session.execute(
        select(
            GroupLeaderInfo,
            User.nickname,
            User.avatar,
            User.uuid
        )
        .join(User, User.id == GroupLeaderInfo.user_id)
    ).all()

    # 6. 处理每个团长的详细信息
    for leader_info, nickname, avatar, uuid in leaders_query:
        leader_id = leader_info.user_id

        # 获取该团长的所有团员ID
        leader_member_ids = db.session.scalars(
            select(GroupMember.member_id)
            .where(GroupMember.leader_id == leader_id)
        ).all()

        # 初始化团长数据
        leader_data = {
            "nickname": nickname,
            "avatar": avatar or current_app.config['DEFAULT_AVATAR_URL'],
            "uuid": uuid,
            "created_at": leader_info.created_at.strftime('%Y-%m-%d %H:%M:%S') if leader_info.created_at else None,
            "invite_code": leader_info.invite_code,
            "total_members": len(leader_member_ids),
            "transaction_orders": 0,  # 成交量
            "transaction_amount": "0.00",  # 成交额
            "settled_commission": "0.00",  # 已发放佣金
            "pending_commission": "0.00"  # 待发放佣金
        }

        if leader_member_ids:
            # 查询该团长的成交量和成交额
            # 首先查询基本成交额（不包含部分退款的订单）
            leader_transaction_stats = db.session.execute(
                select(
                    func.count(SubOrderModel.id).label('total_orders'),
                    func.sum(SubOrderModel.actual_payment).label('total_amount')
                )
                .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
                .where(
                    OrderModel.paid_at.isnot(None),
                    SubOrderModel.status.in_(transaction_statuses),
                    OrderModel.user_id.in_(leader_member_ids)
                )
            ).one_or_none()

            # 初始化该团长的成交量和基本成交额
            leader_orders = leader_transaction_stats.total_orders or 0 if leader_transaction_stats else 0
            leader_amount = leader_transaction_stats.total_amount or Decimal(
                '0.00') if leader_transaction_stats else Decimal('0.00')

            # 处理该团长的售后部分退款订单（只计算未退款部分）
            # 查询部分退款订单
            leader_partial_refund_orders = db.session.execute(
                select(SubOrderModel)
                .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
                .where(
                    OrderModel.paid_at.isnot(None),
                    SubOrderModel.status == SubOrderStatus.AFTER_SALE_PARTIAL_REFUNDED.value,
                    OrderModel.user_id.in_(leader_member_ids)
                )
            ).scalars().all()

            # 计算部分退款订单的未退款金额
            leader_partial_refund_amount = Decimal('0.00')
            for order in leader_partial_refund_orders:
                # 获取订单实际支付金额
                actual_payment = order.actual_payment or Decimal('0.00')

                # 从refund_info JSON字段中获取退款金额
                refund_amount = Decimal('0.00')
                if order.refund_info and isinstance(order.refund_info, dict) and 'refund_amount' in order.refund_info:
                    try:
                        refund_amount = Decimal(str(order.refund_info['refund_amount']))
                    except (ValueError, TypeError, KeyError):
                        # 如果转换失败，使用0
                        pass

                # 计算未退款部分并累加
                non_refunded_amount = actual_payment - refund_amount
                if non_refunded_amount > 0:
                    leader_partial_refund_amount += non_refunded_amount

            # 计算部分退款订单数量
            leader_partial_refund_orders_count = db.session.execute(
                select(func.count(SubOrderModel.id))
                .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
                .where(
                    OrderModel.paid_at.isnot(None),
                    SubOrderModel.status == SubOrderStatus.AFTER_SALE_PARTIAL_REFUNDED.value,
                    OrderModel.user_id.in_(leader_member_ids)
                )
            ).scalar_one_or_none() or 0

            # 更新该团长的总成交量和总成交额
            leader_orders += leader_partial_refund_orders_count
            leader_amount += leader_partial_refund_amount

            leader_data["transaction_orders"] = leader_orders
            leader_data["transaction_amount"] = str(leader_amount)

            # 查询该团长的已发放佣金
            leader_settled_commission = db.session.execute(
                select(func.sum(GroupLeaderCommissionDetail.amount))
                .where(
                    GroupLeaderCommissionDetail.user_id == leader_id,
                    GroupLeaderCommissionDetail.operation_type == 'sub_order',
                    GroupLeaderCommissionDetail.amount > 0
                )
            ).scalar_one_or_none() or Decimal('0.00')

            leader_data["settled_commission"] = str(leader_settled_commission)

            # 计算该团长的待发放佣金
            leader_pending_commission = Decimal('0.00')

            # 获取该团长的所有待结算子订单
            leader_pending_results = db.session.execute(
                select(SubOrderModel.sku_id, SubOrderModel.quantity)
                .join(OrderModel, OrderModel.id == SubOrderModel.order_id)
                .where(
                    OrderModel.user_id.in_(leader_member_ids),
                    OrderModel.paid_at.isnot(None),
                    # 已付款、已发货、已送达的都算待结算
                    SubOrderModel.status.in_([
                        SubOrderStatus.PAID.value,
                        SubOrderStatus.SHIPPED.value,
                        SubOrderStatus.DELIVERED.value
                    ])
                )
            ).all()

            # 获取所有相关SKU的佣金信息
            if leader_pending_results:
                sku_ids = [result.sku_id for result in leader_pending_results]
                from app.models import SKU as SkuModel

                sku_commissions = {}
                sku_commission_results = db.session.execute(
                    select(SkuModel.id, SkuModel.commission)
                    .where(SkuModel.id.in_(sku_ids))
                ).all()

                for result in sku_commission_results:
                    sku_commissions[result.id] = result.commission or Decimal('0.00')

                # 计算预计佣金
                for result in leader_pending_results:
                    sku_commission = sku_commissions.get(result.sku_id, Decimal('0.00'))
                    # 累加预计佣金
                    leader_pending_commission += sku_commission * Decimal(result.quantity)

            leader_data["pending_commission"] = str(leader_pending_commission)

        # 添加到团长列表
        stats["leaders"].append(leader_data)

    # 格式化金额为字符串
    stats["total_amount"] = str(stats["total_amount"])
    stats["total_commission"] = str(stats["total_commission"])

    # 按成交金额从大到小排序团长列表
    stats["leaders"].sort(key=lambda x: Decimal(x["transaction_amount"]), reverse=True)

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": stats
    }
