from flask import Blueprint, request, jsonify, current_app

from app.constants.error_codes import ErrorCode
from app.decorators.auth import auth_required, admin_required
from app.infrastructure.image_service import image_service
from app.utils.errors import ValidationError

# 创建蓝图
bp = Blueprint('image', __name__, url_prefix='/api/images')


def _upload_temp_image():
    """
    临时图片上传接口

    接收图片文件，保存到存储目录并标记为临时状态
    如果请求头中包含 index，则会在返回结果中包含该值
    """
    # 检查是否有文件上传
    if 'file' not in request.files:
        raise ValidationError("未找到上传的文件")

    file = request.files['file']

    # 检查文件是否为空
    if file.filename == '':
        raise ValidationError("未选择文件")

    # 获取过期时间（可选）
    expiry_seconds = None
    expiry_str = request.form.get('expiry_seconds')
    if expiry_str:
        try:
            expiry_seconds = int(expiry_str)
            # 设置最大过期时间限制
            max_expiry = current_app.config.get('MAX_TEMP_IMAGE_EXPIRY', 24 * 60 * 60)  # 默认最大1天
            if expiry_seconds > max_expiry:
                expiry_seconds = max_expiry
        except (ValueError, TypeError):
            # 如果传入的不是有效数字，则使用默认值
            expiry_seconds = None

    # 保存图片并标记为临时
    result = image_service.save_temp_image(file, expiry_seconds)

    # 构造响应
    response_data = {
        "path": result["path"]
    }

    # 如果请求头中包含 index，则添加到返回数据中
    index = request.headers.get('index')
    if index is not None:
        response_data["index"] = index

    response = {
        "code": ErrorCode.SUCCESS.code,
        "data": response_data
    }

    return jsonify(response)


@bp.post('/upload/temp')
@auth_required
def upload_temp_image():
    return _upload_temp_image()


@bp.post('/upload/temp/admin')
@admin_required
def upload_temp_image_admin():
    return _upload_temp_image()


@bp.post('/confirm/<string:image_id>')
@auth_required
def confirm_image(image_id):
    """
    确认图片为永久状态接口

    移除图片的临时标记，使其不会被自动清理
    """
    success = image_service.confirm_permanent(image_id)

    if success:
        response = {
            "code": ErrorCode.SUCCESS.code,
            "message": "图片已确认为永久状态",
            "data": {
                "image_id": image_id
            }
        }
    else:
        response = {
            "code": ErrorCode.DATA_ISSUE.code,
            "message": "图片不存在或已是永久状态",
            "data": {
                "image_id": image_id
            }
        }

    return jsonify(response)
