from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>um, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.extensions import db


class Region(db.Model):
    """地区特色主表"""
    __tablename__ = 'regions'

    id: Mapped[int] = mapped_column(primary_key=True)
    title: Mapped[str] = mapped_column(db.String(100), nullable=False)
    subtitle: Mapped[str] = mapped_column(db.String(200))  # 新增子标题字段
    cover_url: Mapped[str] = mapped_column(db.String(512), nullable=False)
    image_url: Mapped[str] = mapped_column(db.String(512), nullable=False)
    display_order: Mapped[int] = mapped_column(default=0)
    display_type: Mapped[str] = mapped_column(
        db.Enum('small', 'swiper', name='display_type'),
        nullable=False,
        server_default=text("'small'")
    )
    is_active: Mapped[bool] = mapped_column(default=True)

    created_at: Mapped[datetime] = mapped_column(server_default=db.func.now())
    updated_at: Mapped[datetime] = mapped_column(server_default=db.func.now(), onupdate=db.func.now())

    # 关联关系
    products = relationship(
        "Product",
        secondary="region_product_associations",
        back_populates="regions",
        viewonly=True
    )
    associations = relationship(
        "RegionProductAssociation",
        back_populates="region",
        cascade="all, delete-orphan"
    )

    def to_dict(self):
        return {
            "id": self.id,
            "title": self.title,
            "subtitle": self.subtitle,
            "cover_url": self.cover_url,
            "image_url": self.image_url,
            "display_order": self.display_order,
            "display_type": self.display_type,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S") if self.created_at else None,
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S") if self.updated_at else None
        }


class RegionProductAssociation(db.Model):
    """地区商品关联表"""
    __tablename__ = 'region_product_associations'

    region_id: Mapped[int] = mapped_column(ForeignKey('regions.id'), primary_key=True)
    product_id: Mapped[int] = mapped_column(ForeignKey('products.id'), primary_key=True)
    display_type: Mapped[str] = mapped_column(
        Enum('large', 'small', name='display_type'),
        nullable=False
    )
    display_order: Mapped[int] = mapped_column(default=0)

    region = relationship("Region", back_populates="associations")
    product = relationship("Product", back_populates="region_associations")
