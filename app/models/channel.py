from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>um, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.extensions import db


class Channel(db.Model):
    """频道主表"""
    __tablename__ = 'channels'

    id: Mapped[int] = mapped_column(primary_key=True)
    title: Mapped[str] = mapped_column(db.String(100), nullable=False, comment="频道标题")
    cover_url: Mapped[str] = mapped_column(db.String(512), nullable=False, comment="封面图URL")
    display_order: Mapped[int] = mapped_column(default=0, comment="显示顺序（升序排列）")
    display_type: Mapped[str] = mapped_column(
        db.Enum('small', 'swiper', name='display_type'),
        nullable=False,
        server_default=text("'small'"),
        comment="展示类型：small-小图，swiper-轮播"
    )
    custom_images: Mapped[list[str]] = mapped_column(  # 新增字段
        db.JSON,
        comment="自定义图片URL列表",
        nullable=True
    )
    is_active: Mapped[bool] = mapped_column(default=True, comment="是否启用")

    created_at: Mapped[datetime] = mapped_column(server_default=db.func.now())
    updated_at: Mapped[datetime] = mapped_column(server_default=db.func.now(), onupdate=db.func.now())

    # 关联关系
    products = relationship(
        "Product",
        secondary="channel_product_associations",
        back_populates="channels",
        viewonly=True
    )
    associations = relationship(
        "ChannelProductAssociation",
        back_populates="channel",
        cascade="all, delete-orphan"
    )

    def to_dict(self):
        return {
            "id": self.id,
            "title": self.title,
            "cover_url": self.cover_url,
            "display_order": self.display_order,
            "display_type": self.display_type,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S") if self.created_at else None,
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S") if self.updated_at else None
        }

class ChannelProductAssociation(db.Model):
    """频道商品关联表"""
    __tablename__ = 'channel_product_associations'

    channel_id: Mapped[int] = mapped_column(ForeignKey('channels.id'), primary_key=True)
    product_id: Mapped[int] = mapped_column(ForeignKey('products.id'), primary_key=True)
    display_type: Mapped[str] = mapped_column(
        Enum('large', 'small', name='display_type'),
        nullable=False,
        comment="展示类型：large-大展示卡，small-小展示卡"
    )
    display_order: Mapped[int] = mapped_column(default=0, comment="频道内排序")

    # 关联关系
    channel = relationship("Channel", back_populates="associations")
    product = relationship("Product", back_populates="channel_associations")
