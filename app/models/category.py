from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.extensions import db


class Category(db.Model):
    """商品分类模型（支持多级分类）"""
    __tablename__ = 'categories'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(db.String(50), unique=True, index=True)
    parent_id: Mapped[int] = mapped_column(ForeignKey('categories.id'), nullable=True)
    level: Mapped[int] = mapped_column(default=1)  # 分类层级
    sort_order: Mapped[int] = mapped_column(default=0)  # 同级排序

    # 树形关系
    children: Mapped[list["Category"]] = relationship(
        "Category",
        backref="parent",
        remote_side=[id]
    )


class ProductCategoryAssociation(db.Model):
    """商品-分类关联表"""
    __tablename__ = 'product_category_associations'

    product_id: Mapped[int] = mapped_column(
        ForeignKey('products.id'),
        primary_key=True
    )
    category_id: Mapped[int] = mapped_column(
        ForeignKey('categories.id'),
        primary_key=True
    )
    is_primary: Mapped[bool] = mapped_column(default=False)  # 是否主分类
