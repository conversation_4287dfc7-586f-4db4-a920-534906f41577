from datetime import datetime
from decimal import Decimal

from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.extensions import db

# 多对多关联表必须在模型类之前定义
product_tag_associations = db.Table(
    'product_tag_associations',
    db.<PERSON>umn('product_id', db.Integer, db.<PERSON>('products.id'), primary_key=True),
    db.<PERSON>('tag_id', db.Integer, db.ForeignKey('product_tags.id'), primary_key=True)
)

product_guarantee_associations = db.Table(
    'product_guarantee_associations',
    db.Column('product_id', db.Integer, db.Foreign<PERSON>ey('products.id'), primary_key=True),
    db.<PERSON>n('guarantee_id', db.Integer, db.ForeignKey('product_guarantees.id'), primary_key=True)
)


# 产品展示组模型：用于管理首页不同展示区块（如今日主推、即将结束等）
class ProductGroup(db.Model):
    __tablename__ = 'product_groups'
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(db.String(100), nullable=False)  # 展示组名称（如'今日主推'）
    display_order: Mapped[int] = mapped_column(default=0)  # 显示顺序（数字越小排序越靠前）
    is_active: Mapped[bool] = mapped_column(default=True)  # 是否启用该展示组

    # 使用 viewonly 避免关系冲突
    products = db.relationship(
        "Product",
        secondary="product_group_associations",
        back_populates="groups",
        viewonly=True,
        overlaps="group_associations,associations"
    )  # 关联产品列表

    associations = db.relationship(
        "ProductGroupAssociation",
        back_populates="group",
        cascade="all, delete-orphan"
    )


# 产品与展示组关联模型：存储产品在不同展示组的定制化信息
class ProductGroupAssociation(db.Model):
    __tablename__ = 'product_group_associations'

    group_id: Mapped[int] = mapped_column(ForeignKey('product_groups.id'), primary_key=True)
    product_id: Mapped[int] = mapped_column(ForeignKey('products.id'), primary_key=True)

    display_order: Mapped[int] = mapped_column(default=0)  # 组内显示顺序（倒序，数字越大越靠前）

    # 关联关系定义
    group = db.relationship(
        "ProductGroup",
        back_populates="associations",
        overlaps="products"
    )
    product = db.relationship(
        "Product",
        back_populates="group_associations",
        overlaps="groups"
    )


# 产品标签模型：用于商品分类（如"近七日上新"）
class ProductTag(db.Model):
    __tablename__ = 'product_tags'
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(db.String(50), unique=True)  # "标签名称（唯一标识）"
    products: Mapped[list["Product"]] = relationship(
        'Product',
        secondary='product_tag_associations',
        back_populates='tags'
    )  # "关联产品列表"


# 购物保障模型：存储商品保障信息（如极速退款）
class ProductGuarantee(db.Model):
    __tablename__ = 'product_guarantees'
    id: Mapped[int] = mapped_column(primary_key=True)
    title: Mapped[str] = mapped_column(db.String(100))  # "保障标题（如'极速退款'）"
    description: Mapped[str] = mapped_column(db.String(255))  # "保障详细描述"
    products: Mapped[list["Product"]] = relationship(
        'Product',
        secondary='product_guarantee_associations',
        back_populates='guarantees'
    )  # "关联产品列表"


class ProductTrace(db.Model):
    """实地溯源信息"""
    __tablename__ = 'product_traces'

    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(
        db.Integer,
        db.ForeignKey('products.id'),
        unique=True,  # 一对一关系
        nullable=False
    )
    title = db.Column(db.String(100), default="实地溯源")
    images = db.Column(db.JSON, nullable=False)  # 存储图片URL数组
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    product = db.relationship("Product", backref=db.backref("trace", uselist=False))

    def to_dict(self):
        return {
            "id": self.id,
            "title": self.title,
            "images": self.images,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S") if self.created_at else None
        }   


class ProductWiki(db.Model):
    """甄品百科信息"""
    __tablename__ = 'product_wikis'

    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(
        db.Integer,
        db.ForeignKey('products.id'),
        unique=True,
        nullable=False
    )
    title = db.Column(db.String(100), default="甄品百科")
    images = db.Column(db.JSON, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    product = db.relationship("Product", backref=db.backref("wiki", uselist=False))

    def to_dict(self):
        return {
            "id": self.id,
            "title": self.title,
            "images": self.images,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S") if self.created_at else None
        }
    

# 产品图片模型：管理商品图片资源
class ProductImage(db.Model):
    __tablename__ = 'product_images'
    id: Mapped[int] = mapped_column(primary_key=True)
    product_id: Mapped[int] = mapped_column(ForeignKey('products.id'))
    image_url: Mapped[str] = mapped_column(db.String(512))  # 图片URL地址
    is_detail: Mapped[bool] = mapped_column(default=False)  # 是否为详情图片（False表示主图）
    display_order: Mapped[int] = mapped_column(default=0)  # 图片显示顺序


class Product(db.Model):
    __tablename__ = 'products'
    # 添加索引优化查询性能
    __table_args__ = (
        db.Index('idx_published', 'is_published'),
        db.Index('idx_deleted', 'deleted_at'),
        db.Index(
            'idx_fulltext_search',
            'title', 'subtitle', 'description',
            mysql_prefix='FULLTEXT',  # 指定为FULLTEXT索引
            mysql_with_parser='ngram'  # 启用中文分词
        ),
    )

    id: Mapped[int] = mapped_column(primary_key=True)
    created_at: Mapped[datetime] = mapped_column(server_default=db.func.now())
    updated_at: Mapped[datetime] = mapped_column(server_default=db.func.now(), onupdate=db.func.now())

    title: Mapped[str] = mapped_column(db.String(200), nullable=False)  # 短标题，用于瀑布流、商品列表等
    title_image: Mapped[str] = mapped_column(db.String(255), nullable=False)  # 小主图，配合短标题使用

    subtitle: Mapped[str] = mapped_column(db.String(200), nullable=False)  # 子标题

    description: Mapped[str] = mapped_column(db.String(300), nullable=False)  # 描述，用于详情页、商品大展示卡等的文字描述
    description_image: Mapped[str] = mapped_column(db.String(255), nullable=False)  # 大主图，配合详情标题使用

    is_published: Mapped[bool] = mapped_column(default=False)  # 上架状态
    deleted_at: Mapped[datetime] = mapped_column(nullable=True)  # 软删除标记

    points_ratio: Mapped[int] = mapped_column(default=1)  # 积分兑换比例（消费1元可获得积分数量） 示例：10表示1元=10积分
    origin: Mapped[str] = mapped_column(db.String(255))  # 商品溯源产地（三级行政区划格式）示例：'中国·广东·汕头'
    sales_count: Mapped[int] = mapped_column(default=0)  # 累计销售数量（用于商品卡片展示）自动更新
    awards_description = db.Column(
        db.String(255),
        default='',
        comment="所获奖项描述（示例：荣获2023年世界茶叶博览会金奖）"
    )

    shop_id = db.Column(db.Integer, ForeignKey('shops.id'), nullable=False, comment="所属店铺")

    groups = db.relationship(
        "ProductGroup",
        secondary="product_group_associations",
        back_populates="products",
        viewonly=True,
        overlaps="products,associations"
    )
    tags: Mapped[list["ProductTag"]] = relationship(
        'ProductTag',
        secondary='product_tag_associations',
        back_populates='products'
    )
    guarantees: Mapped[list["ProductGuarantee"]] = relationship(
        'ProductGuarantee',
        secondary='product_guarantee_associations',
        back_populates='products'
    )
    images: Mapped[list["ProductImage"]] = relationship()

    skus: Mapped[list["SKU"]] = relationship(back_populates="product", cascade="all, delete-orphan")

    shop = db.relationship('Shop', back_populates='products')

    group_associations = db.relationship(
        "ProductGroupAssociation",
        back_populates="product",
        cascade="all, delete-orphan",
        overlaps="groups,products"
    )

    channels = db.relationship(
        "Channel",
        secondary="channel_product_associations",
        back_populates="products",
        viewonly=True
    )
    channel_associations = db.relationship(
        'ChannelProductAssociation',
        back_populates='product'
    )
    regions = relationship(
        "Region",
        secondary="region_product_associations",
        back_populates="products",
        viewonly=True  # 确保与 Region 的配置一致
    )
    region_associations = relationship(
        "RegionProductAssociation",
        back_populates="product"
    )
    categories = relationship(
        "Category",
        secondary="product_category_associations",
        backref="products",
        viewonly=True
    )
    category_associations = relationship(
        "ProductCategoryAssociation",
        backref="product",
        cascade="all, delete-orphan"
    )

    shipping_locations = relationship(
        "ShippingLocation",
        secondary="shipping_location_product_associations",
        back_populates="products",
        viewonly=True
    )
    shipping_location_associations = relationship(
        "ShippingLocationProductAssociation",
        back_populates="product",
        cascade="all, delete-orphan"
    )


class SKU(db.Model):
    __tablename__ = 'skus'

    id: Mapped[int] = mapped_column(primary_key=True)
    product_id: Mapped[int] = mapped_column(ForeignKey('products.id'))
    sku_code = db.Column(db.String(20), unique=True)  # 唯一业务编码
    display_title: Mapped[str] = mapped_column(db.String(100), nullable=False)
    image_url = db.Column(db.String(512))  # 新增SKU专属图片
    price: Mapped[Decimal] = mapped_column(db.Numeric(10, 2), nullable=False)
    group_price: Mapped[Decimal] = mapped_column(db.Numeric(10, 2), nullable=False)
    postage: Mapped[Decimal] = mapped_column(db.Numeric(10, 2), default=0.0)  # 新增邮费字段
    commission: Mapped[Decimal] = mapped_column(db.Numeric(10, 2), default=0.0)  # 佣金金额（固定金额）
    prehold_stock: Mapped[int] = mapped_column(default=0)  # 预扣库存
    actual_stock: Mapped[int] = mapped_column(default=0)  # 实际库存
    display_order: Mapped[int] = mapped_column(default=0)  # 新增排序字段

    is_published: Mapped[bool] = mapped_column(default=False)  # 上架状态
    deleted_at: Mapped[datetime] = mapped_column(nullable=True)  # 软删除标记
    version: Mapped[int] = mapped_column(default=1)  # 版本号乐观锁

    product = db.relationship("Product", back_populates="skus")

    __table_args__ = (
        db.UniqueConstraint('product_id', 'display_title', name='uq_sku_title'),
        db.Index('idx_sku_code', 'sku_code'),
        db.Index('idx_product_stock', 'product_id', 'actual_stock'),
    )

    def to_dict(self, default_image: str) -> dict:
        """增强序列化方法"""
        return {
            "id": self.id,
            "sku_code": self.sku_code,
            "name": self.display_title,
            "image": self.image_url or default_image,  # 使用默认图片降级
            "price": float(self.price),
            "group_price": float(self.group_price),
            "postage": float(self.postage),
            "commission": float(self.commission),  # 添加佣金字段
            "stock": self.actual_stock,
            "is_stock": bool(self.actual_stock)
        }
