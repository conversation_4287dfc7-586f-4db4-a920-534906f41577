from app.constants import ErrorCode
from app.utils.errors import DomainException


class ProductException(DomainException):
    """商品领域异常基类"""
    def __init__(self, error_code: ErrorCode = None, detail: str = None):
        super().__init__(error_code, detail)


class ProductNotFoundException(ProductException):
    """商品不存在或已下架异常"""
    def __init__(self, detail: str = None):
        super().__init__(ErrorCode.PRODUCT_NOT_FOUND, detail or "商品不存在或已下架")


class StockInsufficientException(ProductException):
    """库存不足异常"""
    def __init__(self, detail: str = None):
        super().__init__(ErrorCode.STOCK_INSUFFICIENT, detail or "商品库存不足")


class SkuNotFoundError(ProductException):
    """SKU不存在或已下架异常"""
    def __init__(self, detail: str = None):
        super().__init__(ErrorCode.SKU_NOT_FOUND, detail or "商品规格不存在或已下架")


class ProductSkuUnavailableException(ProductException):
    """商品或SKU不可用异常"""
    def __init__(self, detail: str = None):
        super().__init__(ErrorCode.PRODUCT_SKU_UNAVAILABLE, detail or "商品规格不可用")


class NoValidSkuError(ProductException):
    """没有有效的SKU异常"""

    def __init__(self, detail: str = None):
        super().__init__(ErrorCode.NO_VALID_SKU, detail)
