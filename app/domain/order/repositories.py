from abc import ABC, abstractmethod
from typing import List, Optional

from .aggregates import Order
from .value_objects import OrderStatus, SubOrderStatus


class OrderRepository(ABC):
    """订单仓储接口"""

    @abstractmethod
    def save(self, order: Order) -> Order:
        """保存订单"""
        pass

    @abstractmethod
    def find_by_id(self, order_id: int) -> Optional[Order]:
        """根据ID查找订单"""
        pass

    @abstractmethod
    def find_by_ids(self, order_ids: List[int]) -> List[Order]:
        """根据ID列表批量查找订单

        Args:
            order_ids: 订单ID列表

        Returns:
            订单聚合根列表
        """
        pass

    @abstractmethod
    def find_by_user_id(self, user_id: int, status: Optional[OrderStatus] = None, page: int = None,
                        per_page: int = None) -> tuple[List[Order], int]:
        """根据用户ID查找订单列表

        Args:
            user_id: 用户ID
            status: 订单状态（可选）
            page: 当前页码（可选，如果提供则启用数据库分页）
            per_page: 每页数量（可选，如果提供则启用数据库分页）

        Returns:
            包含订单聚合根列表和总记录数的元组
        """
        pass

    @abstractmethod
    def find_by_order_no(self, order_no: str) -> Optional[Order]:
        """根据订单号查找订单"""
        pass

    @abstractmethod
    def find_paginated_sub_orders_by_query_and_status(self, order_query: str, status: SubOrderStatus, page: int = 1,
                                                      per_page: int = 20) -> tuple[List[Order], int]:
        """根据订单号和状态查找分页子订单"""
        pass

    @abstractmethod
    def find_paginated_sub_orders_by_query_and_multiple_statuses(self, order_query: str, statuses: list[SubOrderStatus],
                                                                 page: int = 1, per_page: int = 20) -> tuple[
        List[Order], int]:
        """根据订单号和多个状态查找分页子订单"""
        pass

    @abstractmethod
    def find_paginated_sub_orders_by_user_and_multiple_statuses(self, user_id: int, statuses: list[SubOrderStatus],
                                                                page: int = 1, per_page: int = 20) -> tuple[
        List, int]:
        """根据用户ID和多个状态查找分页子订单

        Args:
            user_id: 用户ID
            statuses: 子订单状态枚举值列表
            page: 页码
            per_page: 每页数量

        Returns:
            (list[SubOrder], int): 子订单列表和总数
        """
        pass

    @abstractmethod
    def find_paginated_sub_orders_with_parent_by_user_and_multiple_statuses(self, user_id: int,
                                                                            statuses: list[SubOrderStatus],
                                                                            page: int = 1, per_page: int = 20) -> tuple[
        List, int]:
        """根据用户ID和多个状态查找分页子订单及其父订单

        Args:
            user_id: 用户ID
            statuses: 子订单状态枚举值列表
            page: 页码
            per_page: 每页数量

        Returns:
            (list[tuple[SubOrder, Order]], int): 包含子订单和对应父订单的元组列表，以及总数
        """
        pass

    @abstractmethod
    def update_status(self, order_id: str, status: str) -> bool:
        """更新订单状态"""
        pass

    @abstractmethod
    def exists_by_order_number(self, order_number: str) -> bool:
        """检查订单号是否已存在"""
        pass

    @abstractmethod
    def soft_delete(self, order_id: int) -> bool:
        """软删除订单"""
        pass

    @abstractmethod
    def restore(self, order_id: int) -> bool:
        """恢复软删除的订单"""
        pass

    @abstractmethod
    def count_sub_orders_by_user_and_status(self, user_id: int, status: SubOrderStatus) -> int:
        """统计用户子订单中指定状态的数量"""
        pass

    @abstractmethod
    def hard_delete(self, order_id: int) -> None:
        """硬删除订单及其子订单、订单项

        Args:
            order_id: 订单ID
        """
        pass

    @abstractmethod
    def find_by_tracking_number(self, logistics_company: str, tracking_number: str) -> list[Order]:
        """根据物流单号查找订单

        Args:
            logistics_company: 物流公司编码
            tracking_number: 物流单号

        Returns:
            订单聚合根列表
        """
        pass

    @abstractmethod
    def find_sub_orders_by_status(self, status: SubOrderStatus) -> list:
        """根据状态查找子订单

        Args:
            status: 子订单状态

        Returns:
            子订单列表
        """
        pass
